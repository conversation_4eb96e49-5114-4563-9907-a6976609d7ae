<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="rule-box">
        <video ref="myVideo" class="my-video" src="https://bigitem.oss-cn-zhangjiakou.aliyuncs.com/kabrita/168689/1686892025021301.mp4"></video>
        <div class="btn" @click="playVideo"></div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  myVideo.value?.pause();

  emits('closeDialog');
};
const opendDialog = async () => {};
const myVideo = ref<HTMLVideoElement | null>(null);
const playVideo = () => {
  myVideo.value?.play();
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/349779/31/21618/60965/69086799Fa7ba2f99/3b7ed1237fa557c7.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    margin: 1.4rem 0.35rem 0;
    border-radius: 0.3rem;
    .my-video {
      width: 100%;
      height: 100%;
    }
    .btn {
      width: 4.4rem;
      height: 1rem;
      margin: 0.5rem auto 0;
    }
  }
}
</style>
