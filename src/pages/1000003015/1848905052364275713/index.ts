import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';

initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  disableShare: true,
};
const decoData = {
  limitTotal: 1000,
  discount: 120,
  addBtnColor: '#fff',
  addBtnBg: '#225743',
  addCar: '//img10.360buyimg.com/imgzone/jfs/t1/190305/31/49640/3402/671860a1F47064dfb/02ca76613c5e8368.png',
  joinCar: '//img10.360buyimg.com/imgzone/jfs/t1/135816/21/47276/13420/671860a2Fa5894654/a150f1667793cf3c.png',
  titleBg: '//img10.360buyimg.com/imgzone/jfs/t1/185134/19/50570/14459/671860a2F496e416c/f41e92a0ccf20381.png',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/248376/6/21884/5718/6718ac51Ff7742c94/7ca0412b24138629.png',
  tabsList: [
    {
      tabTile: '//img10.360buyimg.com/imgzone/jfs/t1/249289/23/21672/6310/6719bbe0Fdbaafe56/364b63863e6675c0.png',
      tabActiveTile: '//img10.360buyimg.com/imgzone/jfs/t1/91963/3/53047/5968/6719bbe0Fe2d51b69/a23a5c2b187b2f05.png',
      skuList: [
        {
          skuId: 100142732154,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/199624/38/46870/98476/6719bbdeF9ce6be7e/2132f4b227fbe68e.png',
          skuPrice: 358,
        },
        {
          skuId: 100020111656,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/150117/21/46016/72238/6719bbdeF04c7ff57/963f161402b2633a.png',
          skuPrice: 1402,
        },
        {
          skuId: 100007964437,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/208380/9/44717/53759/6719bbdeFe1debff7/c324b3000adc918f.png',
          skuPrice: 388,
        },
      ],
    },
    {
      tabTile: '//img10.360buyimg.com/imgzone/jfs/t1/188574/12/49724/7132/6719bbdfFd21c8ef8/de8f4d8f33807210.png',
      tabActiveTile: '//img10.360buyimg.com/imgzone/jfs/t1/247934/38/21981/6774/6719bbdfF6d673630/0879e40d7fe69cc9.png',
      skuList: [
        {
          skuId: 100049211158,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/242135/25/20916/124638/6719bbddF5e4c1600/f16c05063f0f4ba5.png',
          skuPrice: 399,
        },
        {
          skuId: 5076794,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/165663/6/51059/74875/6719bbddFdc1c5df4/66d2c4af31eb26ba.png',
          skuPrice: 499,
        },
        {
          skuId: 100086976788,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/169094/7/51990/37382/6719bbdcFc8b4bb7c/1ae02d441b60a265.png',
          skuPrice: 349,
        },
      ],
    },

    {
      tabTile: '//img10.360buyimg.com/imgzone/jfs/t1/16002/5/22333/7319/6719bbdfF92973c3e/c97552f4c383f26c.png',
      tabActiveTile: '//img10.360buyimg.com/imgzone/jfs/t1/97164/13/54222/6891/6719bbdfF6dbb8ead/e916e41e207f9d8d.png',
      skuList: [
        {
          skuId: 100127512834,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/183749/12/50398/102835/6719bbdcF0ee7c7c4/1f933f993d5b2ea8.png',
          skuPrice: 399,
        },
        {
          skuId: 100078050351,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/192911/20/49140/53050/6719bbdbF8b41c1a0/43cb1c3196bf2f7a.png',
          skuPrice: 228,
        },
        {
          skuId: 1322806,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/195236/36/48733/63381/6719bbdbF51afdaa5/66b20077b8362333.png',
          skuPrice: 219,
        },
      ],
    },
    {
      tabTile: '//img10.360buyimg.com/imgzone/jfs/t1/103358/36/52646/6206/6719bbdfF41acb86e/fc38beadf6aa0f3a.png',
      tabActiveTile: '//img10.360buyimg.com/imgzone/jfs/t1/198856/31/46538/5884/6719bbdfFa2d27d88/a7a56937f89abcb9.png',
      skuList: [
        {
          skuId: 5499020,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/101100/33/53320/80351/6719bbdbF88153fe2/5b1312b6a8168498.png',
          skuPrice: 238,
        },
        {
          skuId: 633376,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/246658/28/21044/55273/6719bbdaF27f186d3/e2ce1d62a0fa096c.png',
          skuPrice: 138,
        },
        {
          skuId: 100000060085,
          skuImage: '//img10.360buyimg.com/imgzone/jfs/t1/180869/2/50719/63248/6719bbdaF5206e75f/900bacfa55e1acee.png',
          skuPrice: 96,
        },
      ],
    },
  ],
};
const getEvict = async () => {
  try {
    const { data } = await httpRequest.post('/pechoin/addCart/evict');
    console.log(data);
  } catch (error: any) {
    console.error(error);
  }
};
const getDecoData = async () => {
  try {
    const { data } = await httpRequest.post('/pechoin/addCart/activityContent');
    app.provide('decoData', JSON.parse(data.pageJson));
    // app.provide('decoData', decoData);
  } catch (error: any) {
    console.error(error);
  }
};
init(config).then(async ({ baseInfo, pathParams }) => {
  await getEvict();
  await getDecoData();
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
