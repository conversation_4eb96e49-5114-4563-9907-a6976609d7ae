import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  backActRefresh: false,
  urlPattern: '/custom/:activityType/:templateCode',
};

init(config).then(({ baseInfo, pathParams, decoData, userInfo }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || 'vivo-晒照打卡';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
   app.provide('userInfo', userInfo);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
