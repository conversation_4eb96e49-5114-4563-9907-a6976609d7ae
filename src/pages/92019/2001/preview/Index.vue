<template>
  <div class='bg' :style='furnishStyles.pageBg.value' v-if='isLoadingFinish'>
    <div class='header-kv select-hover'>
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt='' class='kv-img' />
      <div class='header-content'>
        <div class='header-btn header-btn1' @click='rulePopup = true'></div>
        <div class='header-btn header-btn2' @click='myPrizePopup = true'></div>
      </div>
    </div>
    <div class='ac-Introduction' :style='furnishStyles.acIntroductionBg.value'></div>
    <div class='series-box' v-for='(item, index) in seriesList' :key='index'>
      <div class='serise-top' :style="{backgroundImage:`url(${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/313836/18/23643/7541/68956aecFe13e6ecb/bb5728dae4788609.png'})`}">
        {{ item.seriesName }}
      </div>
      <div class='serise-main' :style="{backgroundImage:`url(${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/314370/18/24082/643/68956aecF452fd525/9f1db27544ec2225.png'})`}">
        <span class='serise-tip'>注意:<span style='color: #e52d26'>奖品随时更换，建议锁定奖品尽快完成兑换</span><br />请先选定好自己的目标奖品集罐，因为选择一个奖品领取后罐数会被消耗，需要重新积累获取下一级奖品哦！</span>

        <div class='serise-prize' v-for='(_item,_index) in item.stepList'>
          <div class='serise-prize' v-for='(pitem,pindex) in _item.seriesPrizeList' :key='pindex'>
            <template v-if='pitem.currentShow===1'>
              <div class='serise-title'>购买{{ item.seriesName }}系列商品满{{ _item.potNum }}罐，加赠{{ pitem.prizeName }}</div>
              <div class='serise-stock gradient-heading'><i>限量{{ pitem.sendTotalCount }}份!</i></div>
              <div class='exchange-view'>
                <div class='display-line'>
                  <div class='product-view'>
                    <div style='min-height: 1.8rem;display: flex;align-items: center;justify-content: center'>
                      <img style='width: 1.8rem;' :src='item.seriesPic' alt=''>
                    </div>
                    <img class='buy-btn' @click='showTipToast()' src='//img10.360buyimg.com/imgzone/jfs/t1/324723/15/2039/11054/68956785F6cf7052f/072727ad3273a9e2.png' alt=''>
                    <div class='tank-num'>{{ _item.potNum }}罐</div>
                  </div>
                  <img style='width: .6rem' src='//img10.360buyimg.com/imgzone/jfs/t1/323907/6/2352/1966/6895c8a5F5534385b/44a867756dae79db.png' alt=''>
                  <div class='product-view'>
                    <div style='min-height: 1.8rem;display: flex;align-items: center;justify-content: center'>
                      <img style='width: 1.8rem' :src='pitem.prizeImg' alt=''>
                    </div>
                    <img class='draw-btn' @click='showTipToast()' src='//img10.360buyimg.com/imgzone/jfs/t1/307713/18/23863/7455/68956785Febe2928c/68e7950642808bad.png' alt=''>
                    <div class='price-num' v-if="pitem.unitPrice!==0.01">价值￥<span style='font-size: .26rem'>{{pitem.unitPrice}}</span></div>
                    <div class='prize-stock'>奖品剩余：{{ pitem.sendTotalCount }}</div>
                  </div>
                </div>

                <div class='process-view'>
                  <div class='process-message'>0 / {{ _item.potNum }}</div>
                  <div class='process'></div>
                </div>

                <div class='process-tip'>
                  您已购买 0 罐<br />再买 {{ _item.potNum }} 罐就可以拿到第{{ _index + 1 }}步赠品了哦
                </div>

                <div class='process-tip-2'>
                  *以订单确认收货状态为准，不含小罐奶粉
                </div>
              </div>
            </template>
          </div>

        </div>

        <img class='go-buy-btn' @click='showTipToast()' src='//img10.360buyimg.com/imgzone/jfs/t1/292109/35/27084/7432/68956785F7378a15d/a571b9134f0c537c.png' />
      </div>
      <div class='serise-bottom' :style="{backgroundImage:`url(${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/319866/15/22127/2634/68956aecF0dd9c2c8/0e8d7b8a120e74f6.png'})`}"></div>
    </div>

    <div class='rule-box'>
      <div class='rule-message' v-html='ruleText'></div>
    </div>
  </div>
  <VanPopup teleport='body' v-model:show='rulePopup' :closeOnClickOverlay='false'>
    <Rule @close='rulePopup = false' :rule='ruleText'></Rule>
  </VanPopup>

  <VanPopup teleport='body' v-model:show='myPrizePopup' :closeOnClickOverlay='false'>
    <MyPrize v-if='myPrizePopup' @close='myPrizePopup = false'></MyPrize>
  </VanPopup>
</template>

<script lang='ts' setup>
import { computed, inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import PrizeTip from '../components/PrizeTip.vue';
import MyPrize from '../components/MyPrize.vue';
import Rule from '../components/Rule.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import { deepCopy } from '@/utils/platforms/client';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const isExposure = ref(1);
const isLoadingFinish = ref(false);

const defaultSeriesList = [
  {
    seriesName: '系列名',
    seriesPic: '',
    seriesPrizeList: [],
    seriesSkuList: [
      {
        potNum: 6,
        skuId: 12345678,
      },
    ],
    seriesUrl: '',
  },
];
// 系列列表
const seriesList = ref<any[]>();

const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);

const showTipToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  seriesList.value = data.seriesList;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    ruleText.value = activityData.rules;
    seriesList.value = activityData.seriesList;
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang='scss'>

</style>
