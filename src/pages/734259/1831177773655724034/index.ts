/*
 * @Description: 林某
 */
import '@/style/reset.scss';
import { createApp } from 'vue';
import { init } from '@/utils';
import './style/index.scss';
import 'vant/es/toast/style';
import { initRem } from '@/utils/client';

import root from './App.vue';

import { InitRequest } from '@/types/InitRequest';

initRem(750);

// 设置页面title
document.title = '宠爱生活新主张投票页';

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  disableShare: true,
  shopId: '734259',
  activityMainId: '1831177773655724034',
};
// 初始化页面,
init(config).then(({ baseInfo }) => {

  // app.provide('isOpenCard', isOpenCard);
  app.provide('baseInfo', baseInfo);
  app.mount('#app');
});
