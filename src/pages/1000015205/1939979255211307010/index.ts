import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import App from './index.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import { setToastDefaultOptions, allowMultipleToast } from 'vant';

import '@/style';
import 'animate.css';
import 'vant/lib/index.css';

// 全局设置loading toast配置
setToastDefaultOptions('loading', {
  forbidClick: true,
  duration: 0,
  message: '请稍候',
});
// 全局设置 普通toast配置
setToastDefaultOptions({
  duration: 2000,
  forbidClick: true,
});
// 允许多个toast同时展示
allowMultipleToast();

// 页面自适应
initRem();

const app = createApp(App);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  showFinishedPage: true,
  showUnStartPage: true,
};

init(config).then(({ baseInfo, pathParams, userInfo, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '未命名';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('baseUserInfo', userInfo);
  app.provide('decoData', decoData);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
