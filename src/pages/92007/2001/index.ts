import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  urlPattern: '/custom/:activityType/:templateCode',
  disableNotice: true,
};

const _decoData = {
  kv: '//img10.360buyimg.com/imgzone/jfs/t1/279108/13/27982/340725/6811c63bF4cbc8700/74a62ce10451f222.png',
  gitft1: '//img10.360buyimg.com/imgzone/jfs/t1/296306/12/1294/289768/6819d0ffF3e41bc19/7b8303c7bd1b7218.png',
  gitft2: '//img10.360buyimg.com/imgzone/jfs/t1/294999/10/3364/60933/6819d100F4abb15ac/92fe0f4a10e05cfc.png',
  step: '//img10.360buyimg.com/imgzone/jfs/t1/273224/34/29493/72701/6811c63dF136a025d/d2adc91a40d0edc8.png',
  lockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/277640/23/13451/19645/67ea1002F11fe1267/6837f249ec351f99.png',
  hadLockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/209580/23/42412/14162/665482f0Fb93bd43a/37e816c4c4c38a03.png',
  lockFalseBtn: '//img10.360buyimg.com/imgzone/jfs/t1/231894/31/19337/19165/66558f31F556f7d58/eb46b9c4c0a4838e.png',
  lockFinishBtn: '//img10.360buyimg.com/imgzone/jfs/t1/193910/10/46280/14780/66546f57Febb8f4ca/4bb2d66df814ca2c.png',
  lockNoStart: '//img10.360buyimg.com/imgzone/jfs/t1/186507/24/46943/10695/665d854eF1c92b82a/94f5a3c077ac2e49.png',
  lockHadEnd: '//img10.360buyimg.com/imgzone/jfs/t1/227316/34/19792/10483/665d80beF4aaaefaf/af4c2191c1bbc78c.png',
  skuBK: '//img10.360buyimg.com/imgzone/jfs/t1/271687/28/28088/20617/6811c63dF383661cd/c1428c00bacba9f4.png',
  goBuyBtn: '//img10.360buyimg.com/imgzone/jfs/t1/237758/25/16559/4662/6655b69aF93978327/6b8a66110fbf4ddc.png',
  goShopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/272155/19/12641/23461/67ea1361F1b127826/20dcca90e516a532.png',
  successBk: '//img10.360buyimg.com/imgzone/jfs/t1/171777/30/46572/23666/665e82b1Fa303832a/b6386eee122fb6c0.png',
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/204029/35/42959/26694/6655a9aaF08efa147/48ee85ea92f9064d.png',
  jumpUrl: '',
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo.activityName;
  if (!baseInfo.supportLevels) {
    window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
  }
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('decoData', decoData);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
