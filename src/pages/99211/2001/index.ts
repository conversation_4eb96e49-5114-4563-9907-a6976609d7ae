import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, initPreview } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';
import { preview } from './Utils';
import {   ComSortList, loadingFinish, upDataDecoration,thresholdList } from './Views/DataHooks';
import CLIENT_TYPE, { getClientType, isPC } from '@/utils/platforms/clientType';
import thresholdPlugin from '@/plugins/ThresholdChecker';

initRem();
const app = createApp(root);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableThreshold: true,
  disableShare: true,
  disableNotice: true,
  urlPattern: '/custom/:activityType/:templateCode',
};
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/99211/getActivityInfo');
    ComSortList.value = data.comSortList;
    if (!preview) {
      const updatePromises = ComSortList.value.map((item: any) => upDataDecoration(item));
      await Promise.all(updatePromises);
    }
    loadingFinish.value = true;
  } catch (error: any) {
    console.error('获取活动信息失败:', error.message);
    loadingFinish.value = true;
  }
};
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    return data;
  } catch (error) {
    console.error();
  }
};

if (preview) {
  initPreview(config).then(async () => {
    app.mount('#app');
  });
} else {
  init(config).then(async ({ baseInfo, pathParams, userInfo, decoData }) => {
    await getActivityInfo();
    document.title = baseInfo.activityName;
    thresholdList.value= baseInfo.thresholdResponseList;
    app.provide('decoData', decoData);
    app.provide('baseInfo', baseInfo);
    app.provide('userInfo', userInfo);
    app.provide('ruleText', await getRule());
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, {});
    app.use(thresholdPlugin);
    app.mount('#app');
  });
}
