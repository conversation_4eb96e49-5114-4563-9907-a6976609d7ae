<template>
  <div class="lottery">
    <div class="prize-swiper swiper-container">
      <div class="swiper-wrapper">
        <div class="prize-item swiper-slide" v-for="(item, index) in drawPrizeList" :key="item.prizeId">
          <div class="prize-box">
            <img :src="item.prizeImg" alt="" class="prize-img" />
            <div class="prize-name">{{ item.prizeName }}</div>
          </div>
        </div>
      </div>
    </div>
    <img src="../assets/goods.png" alt="" class="goods" />
    <div class="lottery-prize-btn" @click="startLottery">
      <img src="../assets/lotteryPrizeBtn.png" alt="" class="lottery-prize-btn-img" :class="{ gray: userInfo.drawNum === 0 }" />
      <div class="lottery-num">剩余{{ userInfo.drawNum }}次</div>
    </div>
    <div class="task" @click="toTask"></div>
    <div class="winner-swiper swiper-container">
      <div class="swiper-wrapper">
        <div class="winner-item swiper-slide" v-for="(winner, index) in winners" :key="index">{{ winner.nickName }} 赢得了 {{ winner.prizeName }}</div>
      </div>
    </div>

    <VanPopup teleport="body" v-model:show="resultPop">
      <LotteryResult :prize-info="prizeInfo" @close="resultPop = false" @saveAddress="toSaveAddress" @showPassword="toShowPassword" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="saveAddressPop">
      <SaveAddress v-if="saveAddressPop" :address-id="prizeInfo.result.result" @close="saveAddressPop = false" @success="saveAddressPop = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="copyPasswordPop">
      <CopyPassword :prize-info="prizeInfo.result" @close="copyPasswordPop = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { nextTick, onMounted, ref } from 'vue';
import { Prize, drawPrizeList, getPrizeList, getUserInfo, userInfo } from '../hooks';
import LotteryResult from '../Pop/LotteryResult.vue';
import SaveAddress from '../Pop/SaveAddress.vue';
import CopyPassword from '../Pop/CopyPassword.vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';

Swiper.use([Autoplay]);
const winners = ref<any[]>([]);

const resultPop = ref(false);
const saveAddressPop = ref(false);
const copyPasswordPop = ref(false);

const prizeInfo = ref<any>({});
const startLottery = async () => {
  try {
    if (userInfo.value.drawNum === 0) {
      showToast('抱歉，当前可用次数不足');
      return;
    }
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/brand/yiLiSuChao/lotteryDraw');
    closeToast();
    if (data.status !== 1) {
      data.prizeType = 0;
    }
    prizeInfo.value = data;
    resultPop.value = true;
    getPrizeList(2);
    getUserInfo();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

const toSaveAddress = () => {
  resultPop.value = false;
  saveAddressPop.value = true;
};

const toShowPassword = () => {
  resultPop.value = false;
  copyPasswordPop.value = true;
};

let winnerSwiper: Swiper;
const getAllDrawUserList = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiSuChao/getAllDrawUserList');
    data.forEach((item: any) => {
      item.nickName = item.nickName.length > 2 ? `${item.nickName[0]}***${item.nickName[item.nickName.length - 1]}` : item.nickName;
    });
    winners.value = data;
    if (data.length === 0) {
      return;
    }
    nextTick(() => {
      if (winnerSwiper) {
        winnerSwiper.destroy();
      }
      winnerSwiper = new Swiper('.winner-swiper', {
        direction: 'vertical',
        slidesPerView: 1,
        spaceBetween: 10,
        autoplay: {
          delay: 2000,
          disableOnInteraction: false,
        },
        loop: true,
      });
    });
  } catch (error) {}
};

const toTask = () => {
  const taskElement = document.getElementById('task');
  if (taskElement) {
    taskElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  }
};

onMounted(() => {
  getAllDrawUserList();
  nextTick(() => {
    const swiper = new Swiper('.prize-swiper', {
      slidesPerView: 3,
      spaceBetween: 0,
      // 当前轮播居中
      centeredSlides: true,
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
      },
      loop: true,
    });
  });
});
</script>

<style scoped lang="scss">
.lottery {
  position: relative;
  width: 7.35rem;
  height: 6.54rem;
  background: url('../assets/act2.png') no-repeat;
  background-size: 100%;
  margin: 0 auto 0.25rem;
  padding-top: 1.5rem;
  .prize-swiper {
    width: 6.93rem;
    height: 2.53rem;
    margin: 0 auto;
    overflow: hidden;
    .prize-box {
      width: 2.17rem;
      height: 2.53rem;
      background: url('../assets/prizeBg.png') no-repeat;
      background-size: 100%;
      margin: 0 auto;
      padding: 0.1rem 0.07rem;
      .prize-img {
        width: 2.02rem;
        height: 1.78rem;
        object-fit: contain;
        margin-bottom: 0.2rem;
      }
      .prize-name {
        font-size: 0.25rem;
        color: #1a1a1a;
        line-height: 0.3rem;
        font-family: 'FZY4JW';
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .goods {
    position: absolute;
    top: 4.1rem;
    left: 1.5rem;
    width: 0.57rem;
    z-index: 10;
  }
  .lottery-prize-btn {
    width: 3.52rem;
    height: 0.55rem;
    margin: 0.33rem auto 0;
    // background: url('../assets/lotteryPrizeBtn.png') no-repeat;
    // background-size: 100%;
    position: relative;
    .lottery-prize-btn-img {
      width: 100%;
    }
    .lottery-num {
      position: absolute;
      top: -0.16rem;
      right: -0.45rem;
      width: 1.01rem;
      height: 0.44rem;
      background: url('../assets/lotteryPrizeNum.png') no-repeat;
      background-size: 100%;
      font-size: 0.2rem;
      text-align: center;
      color: #086fed;
      line-height: 0.37rem;
      font-family: 'FZY4JW';
    }
    .gray {
      filter: grayscale(1);
    }
  }
  .task {
    width: 3.78rem;
    height: 0.49rem;
    margin: 0.15rem auto 0.18rem;
  }
  .winner-swiper {
    width: 3.82rem;
    height: 0.34rem;
    overflow: hidden;
    margin: 0 auto;
    .winner-item {
      font-size: 0.2rem;
      color: #003085;
      line-height: 0.34rem;
      font-family: 'FZY4JW';
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
