<template>
  <div class="main-box">
    <div class="prize-list-swiper swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide prize-item" v-for="(item, index) in healthStageList" :key="index">
          <img :src="item.stagePrizeImage" alt="" class="prize-img" />
          <div class="health-status-btn">
            <img v-if="activityInfo.userCurrentHealthValue < item.stageHealth" src="//img10.360buyimg.com/imgzone/jfs/t1/347170/37/10614/2575/68e881a4F979afc06/2a37d314921351a1.png" alt="" />
            <img v-else-if="activityInfo.userCurrentHealthValue >= item.stageHealth && item.isReceived" src="//img10.360buyimg.com/imgzone/jfs/t1/341482/1/10758/2676/68e881a4Fbe6fdd85/7100561f0ba79d73.png" alt="" />
            <img v-else-if="activityInfo.userCurrentHealthValue >= item.stageHealth && !item.isReceived && healthStageList.some((item) => item.isReceived)" src="//img10.360buyimg.com/imgzone/jfs/t1/327053/35/27837/4791/68ea2326F78c35635/0ae88a5fcaebd056.png" alt="" />
            <img v-else-if="activityInfo.userCurrentHealthValue >= item.stageHealth && !healthStageList.some((item) => item.isReceived)" @click="exchangePrize(item)" src="//img10.360buyimg.com/imgzone/jfs/t1/330317/22/21128/2975/68e881a4F96c03970/6da22a9e3680c07d.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/333971/11/18333/2396/68da2e9eF0446ec77/fa9295530de1a41c.png" alt="" class="prev" @click="toPrev" />
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/334259/20/18471/2433/68da3063F6cd9278f/37292443fb07402b.png" alt="" class="next" @click="toNext" />
    </div>
    <div class="user-health-coin">
      <div class="health-num">{{ activityInfo.userCurrentHealthValue }}</div>
      <div class="next-level" v-if="activityInfo.userCurrentHealthValue < healthStageList[healthStageList.length - 1]?.stageHealth">{{ 100 - currentLevelPercent }}%</div>
    </div>
    <div class="progress-bar">
      <div class="progress-inner" :style="{ width: `${progressPercent}%` }"></div>
      <div class="node-list">
        <div class="node-item" v-for="(item, index) in healthStageList" :key="index">
          <div class="node-content">
            <div class="node-name">{{ item.stageName }}</div>
            <img src="//img10.360buyimg.com/imgzone/jfs/t1/350242/22/8426/2067/68d9f0c5Fa21830fd/6e9729cee626aa90.png" alt="" class="node-icon" />
            <div class="node-num">
              <div class="num">
                <span>{{ item.stageHealth }}</span
                >健康币
              </div>
              <div class="prize">（可兑换{{ item.stagePrizeName }}）</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <VanPopup v-model:show="confirmExchangePop" teleport="body" :close-on-click-overlay="false">
    <ConfirmExchange @close="confirmExchangePop = false" @confirm="confirmExchange" :prize-info="confirmExchangeItem"></ConfirmExchange>
  </VanPopup>
  <VanPopup v-model:show="exchangeSuccessPop" teleport="body" :close-on-click-overlay="false">
    <ExchangeSuccess @close="exchangeSuccessPop = false" :prize-info="confirmExchangeItem"></ExchangeSuccess>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import ConfirmExchange from '../Pop/ConfirmExchange.vue';
import ExchangeSuccess from '../Pop/ExchangeSuccess.vue';
import { activityInfo, checkActStatus, getActivityInfo, getHealthStage, healthStageList } from '../hooks';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const confirmExchangePop = ref(false);
const exchangeSuccessPop = ref(false);

const confirmExchangeItem = ref<any>({});
const exchangePrize = (item: any) => {
  if (!checkActStatus()) {
    return;
  }
  confirmExchangeItem.value = item;
  confirmExchangePop.value = true;
};

const confirmExchange = async () => {
  try {
    showLoadingToast({
      message: '兑换中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/94007/receiveStagePrizePrize', {
      prizeId: confirmExchangeItem.value.stageId,
    });
    if (data.status !== 1) {
      showToast('兑换失败');
      return;
    }
    closeToast();
    confirmExchangePop.value = false;
    exchangeSuccessPop.value = true;
    getActivityInfo();
    getHealthStage();
  } catch (error: any) {
    closeToast();
    showToast(error.message || '兑换失败');
  }
};

// 计算当前进度百分比（向下取整）
const progressPercent = computed(() => {
  // 每段占比数组
  const segmentPercent = [20, 30, 30];
  // 找到第一个大于当前健康币的档位
  const nextIndex = healthStageList.value.findIndex((item) => item.stageHealth > activityInfo.userCurrentHealthValue);
  if (nextIndex === -1) {
    // 已超最高档
    return segmentPercent.reduce((acc, cur) => acc + cur, 0);
  }
  if (nextIndex === 0) {
    // 还未到第一档，按第一档比例计算
    return Math.floor(Math.min((activityInfo.userCurrentHealthValue / healthStageList.value[0].stageHealth) * segmentPercent[0], segmentPercent[0]));
  }
  // 在两个档位之间，线性插值
  const prevNum = healthStageList.value[nextIndex - 1].stageHealth;
  const nextNum = healthStageList.value[nextIndex].stageHealth;
  // 累计前 nextIndex - 1 段占比
  const prevBasePercent = segmentPercent.slice(0, nextIndex).reduce((acc, cur) => acc + cur, 0);
  const percentInSegment = (activityInfo.userCurrentHealthValue - prevNum) / (nextNum - prevNum);
  return Math.floor(prevBasePercent + percentInSegment * segmentPercent[nextIndex]);
});

// 计算当前档位所占百分比（向下取整）
const currentLevelPercent = computed(() => {
  const nextIndex = healthStageList.value.findIndex((item) => item.stageHealth > activityInfo.userCurrentHealthValue);
  if (nextIndex === -1) {
    // 已超最高档
    return 100;
  }
  if (nextIndex === 0) {
    // 还未到第一档，计算在第一档内的进度
    return Math.floor((activityInfo.userCurrentHealthValue / healthStageList.value[0].stageHealth) * 100);
  }
  // 在某个档位内，计算在当前档位的进度
  // const prevNum = healthStageList.value[nextIndex - 1].stageHealth;
  const nextNum = healthStageList.value[nextIndex].stageHealth;
  const percentInSegment = activityInfo.userCurrentHealthValue / nextNum;
  return Math.floor(percentInSegment * 100);
});

let prizeSwiper: Swiper;
const toPrev = () => {
  if (prizeSwiper) {
    prizeSwiper.slidePrev();
  }
};
const toNext = () => {
  if (prizeSwiper) {
    prizeSwiper.slideNext();
  }
};
const initSwiper = () => {
  nextTick(() => {
    if (prizeSwiper) {
      prizeSwiper.destroy();
    }
    prizeSwiper = new Swiper('.prize-list-swiper', {
      slidesPerView: 1,
    });
  });
};
watch(
  () => healthStageList.value,
  () => {
    initSwiper();
  },
);
onMounted(() => {
  initSwiper();
});
</script>

<style scoped lang="scss">
.main-box {
  width: 7.19rem;
  height: 7.77rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/334676/24/21303/50813/68e9f96fFa1ce0fff/d0b061bf5297176d.png');
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 1.47rem 0.12rem 0rem 0.13rem;
  position: relative;
  margin: 0 auto 0.36rem;
  .prize-list-swiper {
    width: 100%;
    height: 2.95rem;
    overflow: hidden;
    .prize-img {
      width: 5.52rem;
      margin: 0 auto;
    }
    .health-status-btn {
      height: 0.43rem;
      width: auto;
      position: absolute;
      top: 2.29rem;
      left: 50%;
      transform: translateX(-50%);
      img {
        width: auto;
        height: 100%;
      }
    }
  }
  .prev {
    width: 0.7rem;
    position: absolute;
    top: 2.7rem;
    left: -0.13rem;
    z-index: 20;
  }
  .next {
    width: 0.7rem;
    position: absolute;
    top: 2.7rem;
    right: -0.13rem;
    z-index: 20;
  }
  .user-health-coin {
    position: absolute;
    top: 4.75rem;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .health-num {
    width: 2.44rem;
    height: 0.39rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/340664/31/19200/8016/68e9f90dF7ec86aac/043691c78a9ec5f7.png') no-repeat;
    background-size: 100%;
    text-align: center;
    padding-top: 0.07rem;
    padding-left: 1.5rem;
    color: #fff;
    font-size: 0.315rem;
    line-height: 0.3rem;
  }
  .next-level {
    margin-left: 0.3rem;
    width: 3.33rem;
    height: 0.3rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/342581/15/11509/6433/68e9f90eF389204cf/0660c412317cff11.png') no-repeat;
    background-size: 100%;
    text-align: center;
    padding-left: 0.5rem;
    padding-right: 2.05rem;
    color: #fff;
    font-size: 0.315rem;
    line-height: 0.3rem;
  }
  .progress-bar {
    position: absolute;
    bottom: 1.2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 6.6rem;
    height: 0.32rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/328964/17/25150/750/68d9f0c2F4f29106c/78e17e1b18c87384.png') no-repeat;
    background-size: 100%;
    .progress-inner {
      position: absolute;
      top: 0.05rem;
      left: 0.07rem;
      right: 0.07rem;
      height: 0.22rem;
      background-image: linear-gradient(90deg, rgba(207, 0, 0, 0.5) 0%, rgba(231, 0, 0, 0.74) 47%, rgba(255, 0, 0, 0.97) 94%, #ffffff 100%);
      border-radius: 0.11rem;
      font-size: 0.185rem;
      line-height: 0.22rem;
      text-align: right;
      color: #fff;
      padding-right: 0.16rem;
    }
    .node-list {
      position: absolute;
      left: 0.07rem;
      right: 0.07rem;
      width: 6.46rem;
      top: 0;
      display: flex;
      align-items: center;
      .node-item {
        position: relative;
        height: 0.32rem;
        .node-content {
          position: absolute;
          right: 0;
          transform: translateX(50%);
          .node-icon {
            width: 0.37rem;
            margin-top: -0.04rem;
          }
          .node-name {
            position: absolute;
            top: -0.45rem;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.33rem;
            line-height: 0.33rem;
            width: max-content;
          }
          .node-num {
            position: absolute;
            top: 0.4rem;
            left: 50%;
            transform: translateX(-50%);
            width: max-content;
            text-align: center;
            .num {
              font-size: 0.24rem;
              span {
                color: #860000;
              }
            }
            .prize {
              font-size: 0.155rem;
              color: #5b5b5b;
            }
          }
        }
        &:nth-child(1) {
          width: 20%;
        }
        &:nth-child(2) {
          width: 30%;
        }
        &:nth-child(3) {
          width: 30%;
        }
      }
    }
  }
}
</style>
