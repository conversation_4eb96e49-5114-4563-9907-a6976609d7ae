<template>
  <div class="bg" :style="[furnishStyles.pageBg.value, {minHeight: '100vh', height: 'auto'}]" v-if="isLoadingFinish">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRule = true">
            <div>活动规则></div>
          </div>
        </div>
      </div>
    </div>
    <div class="info">
      <div class="info-title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/279731/11/25270/8287/68071133Ffeac7b45/a9768461dc85ef44.png" alt="">
      </div>
      <div class="info-form">
        <van-form @submit="onSubmit">
          <van-cell-group inset>
            <van-field
              v-if="showNameItem"
              class="field"
              name="姓名"
              label="姓名："
              placeholder="姓名"
              maxlength="0"
              @click="ShowToast"
            />
            <van-field
              v-if="showBirthdayItem"
              v-model="birthday"
              class="field"
              name="生日"
              label="生日："
              readonly
              placeholder="点击选择生日"
              @click="ShowToast"
            />
            <van-field
              v-if="showPhoneItem"
              v-model="phoneNumber"
              class="field"
              name="电话"
              label="电话："
              placeholder="电话"
              maxlength="0"
              @click="ShowToast"
            />
            <van-field
              v-if="showPhoneItem"
              v-model="verificationCode"
              class="field"
              name="验证码"
              label="验证码："
              placeholder="验证码"
              maxlength="0"
              @click="ShowToast"
            />
            <div v-if="showGenderItem" class="gender-radio-group field">
              <div class="gender-label">性别：</div>
              <van-radio-group v-model="gender" direction="horizontal" class="gender-options">
                <van-radio name="男" checked-color="#000" @click="ShowToast">男</van-radio>
                <van-radio name="女" checked-color="#000" @click="ShowToast">女</van-radio>
              </van-radio-group>
            </div>
            <van-field
              v-if="showEmailItem"
              class="field"
              name="邮箱"
              label="邮箱："
              placeholder="邮箱"
              maxlength="0"
              @click="ShowToast"
            />
            <van-field
              v-if="showAreaItem"
              v-model="area"
              is-link
              readonly
              class="field"
              name="住址"
              label="选择地址："
              placeholder="点击选择地址"
              @click="ShowToast"
            />
            <van-field
              v-model="detailAddress"
              class="field"
              name="详细地址"
              label="详细地址："
              placeholder="详细地址"
              maxlength="0"
              @click="ShowToast"
            />
            <div v-for="(item,index) in itemList" :key="index">
              <van-field
                v-if="item.type === 1"
                class="field"
                :name="item.title"
                :label="item.title"
                :placeholder="item.title"
                maxlength="0"
                @click="ShowToast"
              />
            </div>
            <div class="check-box">
              <div class="termCheckbox">
                <van-checkbox v-model="termCheckbox" checked-color="#000" icon-size="12px" @click="ShowToast"></van-checkbox>
                <p>
                  我已阅读并同意馥蕾诗按照<span style="color: #1c2779;" @click="showRule=true">《fresh馥悦荟会员规则》</span> 及 <span style="color: #1c2779;" @click="privacyPolicy = true">《个人信息处理规则》</span>所述处理我的个人信息。
                </p>
              </div>
              <div class="termCheckbox">
                <div style="width: 0.5rem; height: 0.5rem"></div>
                <p>同一用户只可完善一次，为了保障您的会员权益，<span style="color: #1c2779;">请填写正确的生日信息，填写后不可修改。</span></p>
              </div>
            </div>
            <div class="submit" @click="ShowToast"></div>
          </van-cell-group>
        </van-form>
      </div>
    </div>
  </div>

  <!--  时间选择-->
  <van-popup v-model:show="showDatePicker" position="bottom">
    <van-date-picker @confirm="changeBirthday" @cancel="showDatePicker = false" />
  </van-popup>
  <!-- 性别选择已改为单选按钮形式，不再需要弹出层 -->
  <!--  住址选择-->
  <van-popup v-model:show="showArea" position="bottom">
    <van-area
      :area-list="areaList"
      @confirm="changeArea"
      @cancel="showArea = false"
    />
  </van-popup>

  <div v-if="!isCreateImg">
    <!-- 隐私政策 -->
    <VanPopup teleport="body" v-model:show="privacyPolicy">
      <PrivacyPolicy :rule="ruleTest" @close="privacyPolicy = false"></PrivacyPolicy>
    </VanPopup>
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import PrivacyPolicy from '../components/PrivacyPolicy.vue';
import { areaList } from '@vant/area-data';
import { showToast } from 'vant';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);

const showRule = ref(false);
const ruleTest = ref('');
const privacyPolicy = ref(false);

const prizeInfo = ref({
  prizeName: '',
  prizeImg: '',
});
const itemList = ref([]);

const phoneNumber = ref('');

// 生日
const birthday = ref();
const showDatePicker = ref(false);
const changeBirthday = ({ date }:any) => {
  birthday.value = date;
  console.log(birthday.value);
  showDatePicker.value = false;
};

// 性别修改 - 改为单选按钮形式
const gender = ref('');

// 住址修改
const showArea = ref(false);
const area = ref('');
const detailAddress = ref('');
const changeArea = ({ selectedOptions }:any) => {
  showArea.value = false;
  area.value = selectedOptions.map((item:any) => item.text).join('/');
};
const onSubmit = () => {
  console.log('submit');
};

const isStart = ref(false);
const startTime = ref(new Date().getTime());
const endTime = ref(new Date().getTime());

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showAward.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const showNameItem = ref(true);
const showBirthdayItem = ref(true);
const showPhoneItem = ref(true);
const showGenderItem = ref(true);
const showEmailItem = ref(true);
const showAreaItem = ref(true);
const showFreeItem = ref(true);

const resetShowItem = () => {
  showNameItem.value = false;
  showBirthdayItem.value = false;
  showPhoneItem.value = false;
  showGenderItem.value = false;
  showEmailItem.value = false;
  showAreaItem.value = false;
  showFreeItem.value = false;
};

const checkInfoItem = (val:any) => {
  val.forEach((item:any) => {
    if (item.title === '姓名') {
      showNameItem.value = true;
    } else if (item.title === '生日') {
      showBirthdayItem.value = true;
    } else if (item.title === '电话') {
      showPhoneItem.value = true;
    } else if (item.title === '性别') {
      showGenderItem.value = true;
    } else if (item.title === '邮箱') {
      showEmailItem.value = true;
    } else if (item.title === '地址') {
      showAreaItem.value = true;
    }
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;

  } else if (type === 'activity') {
    if (data.prizeList.length) {
      prizeInfo.value.prizeName = data.prizeList[0].prizeName;
      prizeInfo.value.prizeImg = data.prizeList[0].prizeImg;
    }
    startTime.value = new Date(data.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    if (data.itemList.length >= 0) {
      itemList.value = data.itemList;
      resetShowItem();
      checkInfoItem(itemList.value);
    }
    endTime.value = new Date(data.endTime).getTime();
    ruleTest.value = data.rules;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'shop') {
    shopName.value = data;
  }
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.value.prizeImg = activityData.prizeList[0].prizeImg;
    prizeInfo.value.prizeName = activityData.prizeList[0].prizeName;
    itemList.value = activityData.itemList;
    shopName.value = activityData.shopName;
    resetShowItem();
    checkInfoItem(itemList.value);
    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  min-height: 100vh;
  position: relative;
  overflow: visible;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    padding: 0.3rem 0.2rem 0;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.38rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.draw-btn {
  width: 6.9rem;
  margin: 0.4rem auto;

  .count-down {
    position: relative;
    top: -0.56rem;
    left: 0.7rem;
    width: 6rem;
    font-size: 0.25rem;
    color: #f2270c;

    .contentSpan {
      margin-left: 0.39rem;
      display: flex;
      position: absolute;
      top: -0.06rem;
      left: 2.32rem;

      .acblockStyleStyle {
        width: 0.4rem;
        height: 0.44rem;
        color: rgb(242, 39, 12);
        background: rgb(255, 255, 255);
        border-radius: 0.05rem;
        display: flex;
        font-size: 0.25rem;
        justify-content: center;
        align-items: center;
      }

      span {
        width: 0.4rem;
        height: 0.44rem;
        color: rgb(255, 255, 255);
        display: flex;
        font-size: 0.25rem;
        justify-content: center;
        align-items: center;
      }
    }
  }

  img {
    width: 100%;
  }
}

.prizes {
  margin: 0.2rem;
  border-radius: 0.3rem;
  background: rgb(255, 255, 255);

  .gift-logo {
    position: absolute;
    background-repeat: round;
    width: 0.96rem;
    height: 0.96rem;
    img {
      width: 0.96rem;
      height: 0.96rem;
    }
  }

  .gift-title {
    text-align: center;
    font-weight: bold;
    display: flex;
    margin: 0 1.4rem 0 1.4rem;
    font-size: 0.3rem;
    padding-top: 0.21rem;

    span {
      line-height: 0.5rem;
      margin: 0 0.2rem 0 0.2rem;
    }

    img {
      width: 0.4rem;
      height: 0.4rem;
    }
  }

  .gift-show {
    margin-left: 0.9rem;
    margin-top: 0.32rem;
    margin-right: 0.26rem;
    padding-bottom: 0.22rem;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    font-size: 0.25rem;
    align-items: center;

    .gift-img {
      width: 1.2rem;
      height: 1.2rem;
      border: 0.02rem solid gray;
      border-radius: 0.16rem;
      overflow: hidden;
      .imgs{
        width: 1.2rem;
        border-radius: 0.16rem;
        height: auto;
        margin: 0 auto;
      }
    }

    .gift-info {
      display: flex;
      -webkit-box-align: end;
      align-items: flex-end;
    }

    .get-prize-btn {
      width: 1.7rem;
      height: 0.6rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/186680/40/42290/2521/6556d9e2F407f6217/0e14dc897424f9e3.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
}

.info {
  width: 7.18rem;
  height: auto;
  margin: 0.6rem auto 0 auto;
  position: relative;
  top: -2rem;
  z-index: 1;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/280331/10/24426/4313/680852d1Fe06bc0ac/c08ddb74d9f45986.png);
  padding-bottom: 0.5rem;

  .info-title{
    width: 100%;
    padding: 0.35rem;
    img{
      width: 4.02rem;
      margin: 0 auto;
    }
  }

  img {
    width: 6.9rem;
  }

  .info-form{
    width: 7.18rem;
    height: auto;
    min-height: 1rem;
    font-size: 5rem;
    border-radius:0 0 0.3rem 0.3rem;
    overflow: visible;
    .field{
      font-size: 0.25rem;
      --van-field-label-width: 2rem;
    }
    .gender-radio-group {
      display: flex;
      align-items: center;
      padding: 0.2rem 0;
      border-bottom: 1px solid #000;
    }
    .gender-label {
      width: 2rem;
      flex-shrink: 0;
    }
    .gender-options {
      flex: 1;
    }
    .check-box {
      font-size: 0.23rem;
      padding: 0.5rem 0 0;
      .termCheckbox {
        display: flex;
        justify-content: flex-start;
        align-items: start;
        padding-bottom: 0.15rem;
        .van-checkbox {
          width: 0.5rem;
          height: 0.5rem;
        }
        .underline {
          text-decoration: underline;
        }
      }
    }
  }
}
.submit{
  width: 3.03rem;
  height: 0.69rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/279865/34/24619/13731/68071133Fdb4e52b3/38616bdfbebce81f.png);
  background-size: 100% 100%;
  margin: 0.2rem auto 0.2rem auto;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}

.van-cell {
  border-bottom: 1px solid #000;
}
.van-cell-group--inset {
  border-radius: 0;
}
.info-form {
  .van-cell-group {
    background: transparent;
    margin: 0 0.8rem;
  }
  .van-cell {
    background: transparent;
    padding: 0.2rem 0;
  }
}
.van-field__label {
  width: 1.3rem;
}
.van-radio__icon {
  height: auto;
}
.van-radio__icon--dot{
  width: 0.2rem;
  height: 0.2rem;
}
.van-radio__icon--checked.van-radio__icon--dot{
  width: 0.2rem;
  height: 0.2rem;
}
.van-radio__icon--checked.van-radio__icon--dot .van-radio__icon--dot__icon {
  width: 0.1rem;
  height: 0.1rem;
}
</style>
