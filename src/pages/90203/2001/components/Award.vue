<template>
  <div>
    <div class="close-box">
      <img alt=""  src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
      </div>
    <div class='award'>
      <img class="award-bg" src="//img10.360buyimg.com/imgzone/jfs/t1/349338/4/15804/60082/68f8a4f9F7b02597e/0aafef21ff48361a.png" alt="">
      <div class="award-content" v-if="awardPrize.prizeType == 7">
        <img :src="awardPrize.prizeImg" alt="" class="prize-img">
        <div class="input-box">
          <input :value="cardInfo" />
          <div class="copy-btn" :copy-text="cardInfo">复制</div>
        </div>
        <div class="process-btn" @click="showGuide">兑换流程</div>
      </div>
      <div class="award-content" v-else>
        <img :src="awardPrize.prizeImg" alt="" class="prize-img">
        <div class="prize-name">{{ awardPrize.prizeName }}</div>
        <div class="prize-btn" @click="close">我知道了</div>
        <div class="tip" v-html="formatTip(decoData.receiveSuccessTip)"></div>
      </div>
   </div>

  </div>
</template>

<script lang='ts' setup>
import Clipboard from 'clipboard';
import { showToast } from 'vant';
import { computed, inject } from 'vue';

const decoData = inject('decoData') as any;
const props = defineProps(['awardPrize']);

const cardInfo = computed(() => {
  const result = typeof props.awardPrize.result === 'string' ? JSON.parse(props.awardPrize.result) : props.awardPrize.result;
  
  if (result.cardNumber && result.cardPassword) {
    return `卡号：${result.cardNumber} 卡密：${result.cardPassword}`;
  }
  if (result.cardNumber) {
    return `${result.cardNumber}`;
  }
});

const emits = defineEmits(['close', 'showGuide']);

new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const close = () => {
  emits('close');
};

const showGuide = () => {
  const result = typeof props.awardPrize.result === 'string' ? JSON.parse(props.awardPrize.result) : props.awardPrize.result;
  emits('showGuide', result.cardDesc);
};

const formatTip = (text: string) => {
  if (!text) return '';
  // 将【】中的内容替换为红色
  return text.replace(/【([^】]+)】/g, '<span style="color: #ff1902;">【$1】</span>');
};
</script>

<style scoped lang='scss'>
.award {
  width: 80%;
  margin: 0 auto;
  position: relative;
  .award-bg {
    width: 100%;
    object-fit: contain;
  }
  .award-content {
    overflow-y: auto;
    width: 100%;
    height: 6.1rem;
    box-sizing: border-box;
    padding: 0 .7rem;
    position: absolute;
    top: .85rem;
    color: #7c2929;
    display: flex;
    flex-direction: column;
    align-items: center;
    .prize-img {
      width: 3rem;
      height: 3rem;
      border-radius: 0.2rem;
      object-fit: contain;
      background: #fff;
      margin-top: .21rem;
    }
    .prize-name {
      font-size: 0.3rem;
      color: #7c2929;
      text-align: center;
      margin-top: 0.15rem;
      height: .8rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      padding: 0 .2rem;
    }
    .prize-btn {
      font-size: .35rem;
      color: #7c2929;
      text-align: center;
      width: 100%;
      height: 0.7rem;
      background: linear-gradient(to bottom, #ff3621, #ff6758);
      color: #fff;
      border-radius: 0.4rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.4rem;
      border: 1px solid #fff;
      box-shadow: 0 4px 6px 0 rgba(255, 80, 38, 0.33);
    }
    .input-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.35rem;
      width: 4rem;
      height: 0.6rem;
      border-radius: 0.1rem;
      border: 1px solid #ff8f58;
      input {
        width: 3rem;
        outline: none;
        border: none;
        background: transparent;
        padding: 0 .1rem;
      }
      .copy-btn {
        width: 1rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        border-radius: 0.1rem;
        background: linear-gradient(to bottom, #ff3621, #ff6758);
        color: #fff;
        font-size: 0.25rem;
      }
    }
    .process-btn {
      font-size: 0.34rem;
      color: #fff;
      margin-top: 0.4rem;
      width: 4.5rem;
      height: .75rem;
      background: linear-gradient(to top, #ff8f58, #ff6c00);
      border-radius: 0.5rem;
      border: 1px solid #fff;
      box-shadow: 0 4px 6px 0 rgba(255, 80, 38, 0.33);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

}
.close-box {
  display: flex;
  width: 80%;
  margin: 0 auto;
  justify-content: flex-end;
  .close {
    border: 1px solid #fff;
    padding: 0.1rem;
    border-radius: 50%;
  }
}
.tip {
  font-size: 0.15rem;
  color: #999;
  width: 110%;
  margin-top: .2rem;
  text-align: center;
}

</style>
