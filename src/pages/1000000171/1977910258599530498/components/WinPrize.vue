<template>
  <VanPopup v-model:show="isShow">
    <div class="popup-bg">
      <div class="prize-box">
        <div class="prize-img" :style="{backgroundImage: `url(${drawInfo.prizeImg})`}"></div>
      </div>
      <div class="text1">恭喜您<span>{{ drawInfo.prizeType === 3 ? '抽中' : '获得' }}</span
        ></div>
      <div class="text2">
        {{ drawInfo.prizeName }}
        <!-- <span v-if="drawInfo.prizeType !== 3">,奖品已发放</span> -->
      </div>
      <img v-if="drawInfo.prizeType === 3" class="btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/329643/21/22964/10883/68ef09e3F0d6f5047/e22f529d07380bc3.png" alt="" @click="showAddress" />
      <img v-else class="btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/342331/23/12847/11450/68ef09e4Fd8d9406d/f939b84ef8933b29.png" alt="" @click="close" />
      <!-- <div class="text3">请尽快填写地址并联系客服领奖，奖品将在确认收货且未退款后，根据订单实付金额，返还等额E卡</div> -->
      <div class="text3" v-if="drawInfo.prizeType === 3 && drawInfo.prizeName.includes('明星同款')">请尽快填写地址并联系客服领奖，奖品将在确认收货且未退款后，根据订单实付金额，返还等额E卡</div>
      <div class="text3" v-else-if="drawInfo.prizeType === 2">奖品已发放, 请前往<span style="color: #fff478;">我的-京豆</span>查看</div>
      <div class="text3" v-else-if="drawInfo.prizeType === 3 && !drawInfo.prizeName.includes('明星同款')">
        请尽快填写地址<br />
        奖品将在活动结束后60个工作日发放
      </div>
      <div class="text3" v-else>奖品已发放</div>
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/288245/31/9579/1772/6836db46Fb2c9f682/860212cf7162771c.png" @click="close" />
    </div>
  </VanPopup>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';

const props = defineProps({
  showWinPrize: {
    type: Boolean,
    default: false,
  },
  drawInfo: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close', 'showAddress']);
const close = () => {
  emit('close');
};
const showAddress = () => {
  emit('showAddress');
};
const isShow = computed(() => props.showWinPrize);
</script>
<style lang="scss" scoped>
.popup-bg {
  width: 5.91rem;
  height: 6.72rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/334279/19/22308/28193/68ee2499F90596fc1/25482a82fda1d99e.png') no-repeat;
  background-size: 100%;
  padding-top: 1.4rem;
  margin-top: 0.5rem;
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0.1rem;
    right: 0.2rem;
  }
  .text1,
  .text2,
  .text3 {
    font-size: 0.24rem;
    color: #fff;
    text-align: center;
  }
  .text1 {
    margin-bottom: 0.1rem;
    margin-top: 0.3rem;
  }
  .text2 {
    margin-bottom: 0.5rem;
    font-size: 0.32rem;
  }
  .text3 {
    font-size: 0.16rem;
    width: 86%;
    margin: 1.6rem auto 0;
    font-weight: 200;
  }
  .prize-box {
    width: 2.33rem;
    height: 2.37rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/343608/27/11908/3701/68ee2531Fcc1c0530/e1cf4114254461a2.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    box-sizing: border-box;
    padding-top: 0.28rem;
    padding-left: 0.2rem;
    padding-right: 0.17rem;
    margin: auto;
  }
  .prize-img {
    width: 100%;
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    margin: 0 auto;
  }
  .btn {
    position: absolute;
    bottom: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.72rem;
  }
}
</style>
