<template>
  <div class="task" id="task">
    <div class="add-shop-cart" @click="addShopCart" :class="{ gary: userInfo.isAddCart || userInfo.isAddCartThreeDays }">
      <img src="../assets/addCartBtn.png" alt="" />
    </div>
    <div class="to-buy" @click="toBuyPop = true"></div>

    <VanPopup teleport="body" v-model:show="addShopCartPop">
      <AddShoppingCart @close="addShopCartPop = false" @success="addShopCartSuccess" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="toBuyPop">
      <ToBuy @close="toBuyPop = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import AddShoppingCart from '../Pop/AddShoppingCart.vue';
import ToBuy from '../Pop/ToBuy.vue';
import { getUserInfo, userInfo } from '../hooks';
import { BaseInfo } from '@/types/BaseInfo';
// 任务组件逻辑

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const addShopCartPop = ref(false);
const toBuyPop = ref(false);

if (!userInfo.value.isAddCartThreeDays && !userInfo.value.isAddCart && baseInfo.status === 2) {
  addShopCartPop.value = true;
}

const addShopCart = () => {
  if (userInfo.value.isAddCartThreeDays || userInfo.value.isAddCart) {
    return;
  }
  addShopCartPop.value = true;
};

const addShopCartSuccess = () => {
  getUserInfo();
  addShopCartPop.value = false;
};
</script>

<style scoped lang="scss">
.task {
  width: 7.35rem;
  height: 4.63rem;
  background: url('../assets/act4.png') no-repeat;
  background-size: 100%;
  margin: 0 auto 0.25rem;
  padding-top: 1.5rem;
  position: relative;
  .add-shop-cart {
    position: absolute;
    top: 1.9rem;
    right: 0.5rem;
    width: 1.55rem;
    height: 0.72rem;
    img {
      width: 100%;
    }
  }
  .to-buy {
    position: absolute;
    top: 3.4rem;
    right: 0.6rem;
    width: 1.55rem;
    height: 0.72rem;
  }
  .gary {
    filter: grayscale(1);
  }
}
</style>
