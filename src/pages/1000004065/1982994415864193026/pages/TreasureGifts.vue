<template>
  <div class="treasure-gifts">
    <div class="title">有机会前往发布会见雨琦本人</div>
    <div class="tips">每探索一次星光宝藏消耗200点星光值</div>
    <img v-show="isloading" class="motion" src="../assets/treasure/motion.gif" alt="" />
    <div class="btn-wrapper" @click="handleDraw">
      <img :class="{ active: starValue >= 200 }" src="../assets/treasure/square.png" alt="" />
      点击立刻探索
    </div>
    <div class="star-info">
      <img src="../assets/surprise/star.png" alt="" />我的可用星光值: <span>{{ starValue }}</span>
    </div>
    <div class="prize-list">
      <div class="prize-swipe">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in prizeList" :key="item.prizeName">
            <img :src="item.prizeImg" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <DrawFailed :isShowPopup="isShowDrawFailPopup" @closeDialog="isShowDrawFailPopup = false" />
  <DrawSuccess :prizeInfo="drawGift" :isShowPopup="isShowDrawSuccessPopup" @closeDialog="isShowDrawSuccessPopup = false" />
</template>
<script lang="ts" setup>
import { ref, nextTick, onMounted, onUnmounted } from 'vue';
import { showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import DrawSuccess from '../components/DrawSuccess.vue';
import DrawFailed from '../components/DrawFailed.vue';
import { drawLottery, getPrizeList } from '../script/ajax';
import { PrizeInfo } from '../script/type';

const props = defineProps({
  starValue: { type: Number, required: true },
});
const isShowDrawSuccessPopup = ref(false);
const isShowDrawFailPopup = ref(false);
const isloading = ref(false);
let mySwiper: Swiper | null = null;

Swiper.use([Autoplay]);

const initSwiper = () => {
  mySwiper = new Swiper('.prize-swipe', {
    slidesPerView: 4,
    spaceBetween: 10,
    loop: true,
    autoplay: {
      delay: 2000,
      // 防止在不可见时继续播放
      disableOnInteraction: false,
    },
  });
};
const prizeList = ref<PrizeInfo[]>([]);
// 在组件挂载后初始化 Swiper
onMounted(async () => {
  prizeList.value = await getPrizeList();
  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    initSwiper();
  });
});
const emit = defineEmits(['drawSuccess']);
onUnmounted(() => {
  if (mySwiper) {
    mySwiper.destroy(true, true);
  }
});
const drawGift = ref({});
const handleDraw = async () => {
  if (isloading.value) return;
  if (props.starValue < 200) {
    showToast('抱歉，星光值不足，做任务积累更多星光值，解锁丰富奖品哦!');
    return;
  }
  //   开始抽奖
  isloading.value = true;
  const result = (await drawLottery()) as any;
  console.log('🚀 ~ handleDraw ~ result:', result);
  isloading.value = false;
  if (result.status === 0) {
    isShowDrawFailPopup.value = true;
  } else {
    drawGift.value = result;
    isShowDrawSuccessPopup.value = true;
  }
  emit('drawSuccess');
};
</script>
<style lang="scss" scoped>
.treasure-gifts {
  width: 7.5rem;
  height: 8.06rem;
  background: url(../assets/treasure/treasure.png) no-repeat;
  background-size: contain;
  padding-top: 2.23rem;
  box-sizing: border-box;
  text-align: center;
  position: relative;
  font-family: 'OPPOSansM';

  .motion {
    width: 6.76rem;
    position: absolute;
    left: 0.365rem;
    top: 2.72rem;
    z-index: 1;
  }
  .title {
    font-size: 0.26rem;
    font-family: 'OPPOSansM';
  }
  .tips {
    font-size: 0.24rem;
    color: #4b2179;
    margin-top: 0.12rem;
  }
  .btn-wrapper {
    position: absolute;
    font-family: 'OPPOSansM';
    top: 5rem;
    right: 0.5rem;
    background: url(../assets/treasure/btn-bg.png) no-repeat;
    background-size: contain;
    color: #fff;
    width: 1.69rem;
    line-height: 0.36rem;
    font-size: 0.18rem;
    img {
      position: absolute;
      left: -0.15rem;
      width: 0.36rem;
      @keyframes iconAni {
        0% {
          transform: scale(1.5);
        }
        100% {
          transform: scale(1);
        }
      }
      &.active {
        animation: iconAni 0.5s linear infinite;
      }
    }
  }
  .star-info {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    font-size: 0.2rem;
    box-shadow: 0.02rem 0.02rem 0.09rem 0rem rgba(86, 57, 169, 0.21);
    border-radius: 0.4rem;
    position: absolute;
    left: 50%;
    top: 5.63rem;
    transform: translateX(-50%);
    padding: 0.1rem 0.32rem;
    z-index: 1;
    font-family: 'OPPOSansR';
    box-sizing: border-box;
    span {
      color: #6c6aa4;
      font-family: 'OPPOSansM';
      font-size: 0.24rem;
    }
    img {
      width: 0.25rem;
      margin-right: 0.05rem;
    }
  }
  .prize-list {
    margin: 3.5rem 0.42rem 0;
    background: url(../assets/treasure/prize-bg.png) no-repeat;
    background-size: contain;
    padding: 0.15rem 0.6rem 0;
    box-sizing: border-box;
    width: 6.81rem;
    height: 1.43rem;
    .prize-swipe {
      width: 5.46rem;
      margin-left: 0.13rem;
      overflow-x: hidden;
      .swiper-slide {
        width: 1rem;
        height: 1.12rem;
        background-color: #ffffff;
        // padding: 0.2rem;
        box-sizing: border-box;
        border-radius: 0.2rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
}
</style>
