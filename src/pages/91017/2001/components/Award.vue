<template>
  <div class="box">
    <div class="dialog1">
      <div class="cloaseDiv" @click="closeClick()"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineEmits, inject } from "vue";
import type { BaseInfo } from "@/types/BaseInfo";

const baseInfo = inject("baseInfo") as BaseInfo;
const emits = defineEmits(["closeDialog"]);
const closeClick = () => {
  console.log("关闭==========");
  emits("closeDialog");
};
</script>
<style lang="scss" scoped>
.box {
  position: relative;
  .dialog1 {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/331759/1/27538/109989/69007550F85167ea3/71094f18161b1cf7.png)
      no-repeat;
    background-size: 100%;
    width: 7.36rem;
    height: 10.45rem;
    padding-top: 1.6rem;
    box-sizing: border-box;
    position: relative;
    font-family: AlibabaPuHuiTi-Regular;
    .cloaseDiv {
      position: absolute;
      width: 0.6rem;
      height: 0.6rem;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/349625/24/17329/1935/69006e1eF238165e9/ec2db438a8a12b40.png)
        no-repeat;
      background-size: 100%;
      top: 0.5rem;
      right: 0.5rem;
      // background-color: red;
      z-index: 10;
    }
    .confirm-btn1 {
      // background-color: red;
      font-size: 0;
      width: 3rem;
      height: 0.8rem;
      position: absolute;
      bottom: 0.8rem;
      // left: 1.7rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
