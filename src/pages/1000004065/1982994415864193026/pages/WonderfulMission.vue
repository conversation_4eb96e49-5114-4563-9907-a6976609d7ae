<!--
* @Description: caoshijie
* @Date: 2025-10-30 15:05:55
* @Description: 奇妙任务
* @FilePath: src\pages\1000004065\1982994415864193026\components\WonderfulMission.vue 
-->
<template>
  <div class="wonderful-mission">
    <div class="task-1">
      <div class="message-btn" @click="isShowMessageDialog = true">我的留言&gt;</div>
      <div class="task-1-info">
        <div class="task-title">任务一</div>
        <div class="task-des">琦遇时空</div>
        <div class="task-tips">写下你的留言，有机会<br />在发布会上 <span>被雨琦看见</span>哦<br /><span>并获得100个星光值</span>，每日限1次</div>
        <div class="task-btn" @click="isShowWriteDialog = true">写留言</div>
      </div>
      <div class="task-1-img">
        <WordCloud></WordCloud>
      </div>
    </div>
    <div class="task-box">
      <div class="task-2">
        <div class="task-2-title">任务二</div>
        <div class="task-2-info">
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/346245/2/20208/22370/69034d85F47df9797/84a0a2758d72143f.png" alt="" class="task-img" />
          <div class="task-des">与雨琦同框</div>
          <div class="task-tips">参与合拍并发布小红书/分享朋友圈<br />获得<span>50个星光值</span>，每日限1次</div>
          <div class="task-btn" @click="handleShowCompositePhoto">参与合拍</div>
        </div>
      </div>
      <div class="task-2">
        <div class="task-2-title">任务三</div>
        <div class="task-2-info">
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/357969/12/735/6472/69034d86F8c93933b/a722823db5995908.png" alt="" class="task-img" />
          <div class="task-des">体验一键破框</div>
          <div class="task-tips">完成体验<br />获得<span>50个星光值</span>，每日限1次</div>
          <div class="task-btn" @click="handleShowVideo">一键破框</div>
        </div>
      </div>
    </div>
    <div class="task-4">
      <div class="task-4-til">
        <div class="task-2-title">任务四</div>
        更多任务
      </div>
      <div class="task-list">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in taskList" :key="`${item}-index`">
            <div class="task-info">
              <div class="task-til">
                {{ item.title }}
                <span v-if="item.totalNum">({{ item.finishNum }}/{{ item.totalNum }})</span>
              </div>
              <div class="task-des" v-html="item.info"></div>
            </div>
            <div class="task-btn" v-click-track="item.title" :class="{ 'btn-disabled': item.isFinished }" @click="handleTaskItem(item)">{{ item.isFinished ? item.hasdBtn : item.btn }}</div>
          </div>
        </div>
      </div>
      <div class="scroll-tip">&lt;&lt; 左右滑动查看更多任务 &gt;&gt;</div>
    </div>
  </div>
  <MyMessage :isShowPopup="isShowMessageDialog" @close-dialog="isShowMessageDialog = false"></MyMessage>
  <WriteComment :isShowPopup="isShowWriteDialog" @close-dialog="isShowWriteDialog = false"></WriteComment>
  <WatchVideo :isShowPopup="isShowWatchVideoDialog" @close-dialog="isShowWatchVideoDialog = false"></WatchVideo>
  <AddCar @success="handleSkuSuccess" :isShowPopup="isShowSkudialog.isShowAddCarDialog" @close-dialog="isShowSkudialog.isShowAddCarDialog = false"></AddCar>
  <ReservationGoods :isShowPopup="isShowSkudialog.isShowReservationDialog" @close-dialog="isShowSkudialog.isShowReservationDialog = false"></ReservationGoods>
  <BrowseGoods @success="handleSkuSuccess" :isShowPopup="isShowSkudialog.isShowBrowseDialog" @close-dialog="isShowSkudialog.isShowBrowseDialog = false"></BrowseGoods>
  <PlaceOrder  :isShowPopup="isShowSkudialog.isShowPlaceOrderDialog" @close-dialog="isShowSkudialog.isShowPlaceOrderDialog = false"></PlaceOrder>
</template>
<script lang="ts" setup>
import { ref, nextTick, onMounted, reactive, inject } from 'vue';
import WordCloud from '../components/WordCloud.vue';
import MyMessage from '../components/MyMessage.vue';
import { dailyCommonTask, fellowShop, getTaskList } from '../script/ajax';
import { TaskInfo } from '../script/type';
import WriteComment from '../components/WriteComment.vue';
import WatchVideo from '../components/WatchVideo.vue';
import BrowseGoods from '../components/BrowseGoods.vue';

import { gotoSkuPage } from '@/utils/platforms/jump';
import { showToast } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';
import ReservationGoods from '../components/ReservationGoods.vue';
import AddCar from '../components/AddCar.vue';
import { callShare } from '@/utils/platforms/share';
import PlaceOrder from '../components/PlaceOrder.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as any;
console.log('🚀 ~ decoData:', decoData);
const isShowMessageDialog = ref(false);
const isShowWriteDialog = ref(false);
const isShowWatchVideoDialog = ref(false);
const isShowSkudialog = reactive({ isShowBrowseDialog: false, isShowReservationDialog: false, isShowAddCarDialog: false, isShowPlaceOrderDialog: false });

const taskList = reactive<TaskInfo[]>([]);
const taskInit = async () => {
  const result = await getTaskList();
  const { taskInfo } = decoData;
  const mergedTasks = result.map((item: TaskInfo) => {
    const matchedTask = taskInfo.find((task: any) => task.taskType === item.taskType);
    return matchedTask ? { ...item, ...matchedTask } : item;
  });

  Object.assign(taskList, mergedTasks);
};
onMounted(async () => {
  taskInit();
});
const emits = defineEmits(['doTaskSuccess', 'doSkuTask', 'doCompositePhoto']);
const daTaskByType = async (type: number) => {
  console.log('🚀 ~ daTaskByType ~ type:', type);
  //   3 5 7 111 888
  if ([3, 4, 5, 7, 111, 888].includes(type as any)) {
    console.log('🚀 ~ daTaskByType ~ type:', type);
    const result = await dailyCommonTask(type as 3 | 4 | 5 | 7 | 111 | 888, false);
    console.log('🚀 ~ daTaskByType ~ result:', result);
    taskInit();
    emits('doTaskSuccess');
  }
};
// 破框视频
const handleShowVideo = async () => {
  isShowWatchVideoDialog.value = true;
  daTaskByType(888);
};
const doTaskByType: {
  [key: number]: (taskType: TaskInfo) => void;
} = {
  // 1-关注店铺
  1: async (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 1:关注店铺');
    if (item.isFinished) return;
    const result = await fellowShop();
    if (!result) return;
    showToast('关注成功');
    const timer = setTimeout(() => {
      clearTimeout(timer);
      taskInit();
      emits('doTaskSuccess');
    }, 2000);
  },
  // 3-浏览商品
  3: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 3:浏览商品');
    isShowSkudialog.isShowBrowseDialog = true;
  },
  // 4-浏览直播间
  4: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 4:浏览直播间');
    window.location.href = item.pageLink;
  },
  // 5-浏览会场
  5: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 5:浏览会场');
    window.location.href = item.pageLink;
  },
  // 7-加购
  7: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 7:加购');
    isShowSkudialog.isShowAddCarDialog = true;
  },
  // 8-下单
  8: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 8:下单');
    isShowSkudialog.isShowPlaceOrderDialog = true;
    
  },
  // 13-开卡
  13: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 13:开卡', baseInfo.openCardLink);
    const link = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.origin}${window.location.pathname}?isJoin=1`)}`;
    console.log('🚀 ~ link:', link);
    window.location.href = `${baseInfo.openCardLink}`;
  },
  // 111-搜索任务
  111: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 111:搜索任务');
    window.location.href = item.pageLink;
  },
  // 222-预约新品任务
  222: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 222:预约新品任务');
    isShowSkudialog.isShowReservationDialog = true;
  },
  // 333-下单1元权益包
  333: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 333:下单1元权益包');
    gotoSkuPage(item.pageLink);
  },
  // 555-邀请好友
  555: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 555:邀请好友');
    const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareContent,
      imageUrl: shareConfig.shareImage,
      afterShare: () => {},
    });
  },
  // 666-留言
  666: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 666:留言');
    isShowWriteDialog.value = true;
  },
  // 999-学生认证
  999: (item: TaskInfo) => {
    console.log('🚀 ~ doTaskByType ~ 999:学生认证');
    window.location.href = item.pageLink;
  },
};

// 做任务
const handleTaskItem = (item: TaskInfo) => {
  console.log('🚀 ~ handleTaskItem ~ item:', item);
  //   3 7  浏览和加购商品 不直接调用接口  在查看商品弹窗内处理
  if (![3, 7].includes(item.taskType)) {
    !item.isFinished && daTaskByType(item.taskType);
  }
  doTaskByType[item.taskType]?.(item);
};
// 浏览 加购 预约商品成功
const handleSkuSuccess = () => {
  console.log('🚀 ~ handleSkuSuccess ~ success:');
  taskInit();
  emits('doTaskSuccess');
};
const handleShowCompositePhoto = () => {
  emits('doCompositePhoto');
};
</script>
<style lang="scss" scoped>
.wonderful-mission {
  width: 7.06rem;
  height: 13.16rem;
  margin: 0.4rem auto;
  background: url(../assets/mission/task-bg.png) no-repeat;
  background-size: 100% 100%;
  padding-top: 0.97rem;
  box-sizing: border-box;
  position: relative;
  font-family: 'OPPOSansM';
  .message-btn {
    position: absolute;
    right: 0rem;
    top: 0.9rem;
    background-image: linear-gradient(90deg, #a790da 26%, #ccb4e7 100%);
    border-radius: 0.2rem 0 0 0.2rem;
    padding: 0.1rem 0.12rem;
    box-sizing: border-box;
    line-height: 1;
    font-size: 0.18rem;
    color: #fff;
    z-index: 1;
  }
  .task-btn {
    background: url(../assets/mission/btn-bg.png) no-repeat;
    background-size: contain;
    width: 1.66rem;
    line-height: 0.5rem;
    height: 0.55rem;
    text-align: center;
    font-size: 0.24rem;
    color: #ffffff;
    margin-top: 0.2rem;
  }
  .task-des {
    font-size: 0.3rem;
    font-family: 'OPPOSansR';
    margin: 0.36rem 0 0.2rem;
    font-weight: bold;
  }
  .task-tips {
    font-size: 0.18rem;
    font-family: 'OPPOSansR';
    span {
      color: #7654bf;
    }
  }
  .task-1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0.54rem;

    .task-1-info {
      flex: 1;
      .task-title {
        font-size: 0.24rem;
        color: #5732a3;
      }
    }
    .task-1-img {
      width: 3.08rem;
    }
  }
  .task-2-title {
    background-image: linear-gradient(0deg, #b8c5e6 0%, #ffffff 100%);
    border-radius: 0.34rem 0 0.2rem 0;
    font-size: 0.24rem;
    color: #5732a3;
    padding: 0.1rem 0.3rem;
    display: inline-block;
    line-height: 1;

    margin-bottom: 0.1rem;
  }
  .task-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 0.1rem;
    margin: 0.2rem 0.3rem;

    .task-2 {
      flex: 1;
      background-image: linear-gradient(90deg, #e7ecf9 0%, #ffffff 100%);
      border-radius: 0.34rem;

      .task-2-info {
        margin: 0 0.2rem;
        .task-des {
          font-weight: bold;
          margin-top: 0.1rem;
        }
        .task-btn {
          margin: 0.1rem 0 0.2rem;
        }
      }
      .task-img {
        width: 100%;
      }
    }
  }
  .task-4 {
    width: 6.45rem;
    margin: 0.14rem auto;
    background-image: linear-gradient(90deg, #e7ecf9 0%, #fff 100%);
    border-radius: 0.34rem;
    padding-bottom: 0.2rem;
    box-sizing: border-box;
    .task-4-til {
      display: flex;
      font-size: 0.3rem;
      gap: 0.3rem;
    }
  }
  .task-list {
    width: 94%;
    margin: 0.1rem auto;
    .swiper-wrapper {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      height: 2.8rem;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
    }
    .swiper-slide {
      width: 95%;
      height: 1.29rem;
      margin-bottom: 0.1rem;
      margin-right: 0.2rem;
      scroll-snap-stop: always;
      scroll-snap-align: start;
      background: url(../assets/mission/task-list-bg.png) no-repeat;
      background-size: 100% 100%;
      padding: 0.2rem 0.15rem;
      box-sizing: border-box;
      box-shadow: 0 0 0.05rem #cad5f1;
      border-radius: 0.2rem;
      display: flex;
      gap: 0.2rem;
      .task-info {
        width: 3.7rem;

        .task-til {
          font-size: 0.26rem;
        }
        .task-des {
          font-size: 0.18rem;
          margin: 0;
        }
      }
    }
  }
  .scroll-tip {
    text-align: center;
    font-family: 'OPPOSansR';
    line-height: 0.1rem;
    color: #282828;
    font-size: 0.16rem;
  }
}
</style>
