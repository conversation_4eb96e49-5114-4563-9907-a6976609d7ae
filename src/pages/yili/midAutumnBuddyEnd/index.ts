import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import { closeToast, showLoadingToast } from 'vant';

initRem();

window.jmfe.configNavigationBar({
  supportTran: '0',
});

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  // disableShare: true,
  shopId: '1000013402',
  activityMainId: '1962450685376659458',
  errorPageUrl: `${process.env.VUE_APP_HOST}yili/midAutumnBuddy/errorPage/`,
};
showLoadingToast({
  forbidClick: true,
  duration: 0,
});
init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
  // 设置页面title
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('userInfo', userInfo);
  app.use(EventTrackPlugin, {});
  closeToast();
  app.mount('#app');
});
// app.mount('#app');
