<template>
  <div v-if="!showUpload">
    <div class="basePage" :style="furnishStyles.pageBg.value">
      <HomePage v-if="currentPage === 0" @operType="operTypeEmit" />
      <ExamplePage v-else-if="currentPage === 1" />
      <RankingPage v-else-if="currentPage === 2" />
      <MyPage v-else-if="currentPage === 3" @toUploadPage="showUpload = true" @changeMyImages="changeMyImages" />
    </div>
    <div class="navBar">
      <van-tabbar
        v-model="active"
        class="tabbarBox"
        :style="furnishStyles.bottomNavImgBg.value"
      >
        <img @click="uploadClick" class="uploadIcon" :src="furnish.uploadIcon" alt="" />
        <van-tabbar-item
          v-for="(item, index) in iconList"
          :key="index"
          @click="switchPage(index)"
        >
          <template #icon="">
            <img
              style="height: 0.82rem"
              :src="active === index ? item.icon.active : item.icon.inactive"
            />
          </template>
        </van-tabbar-item>
      </van-tabbar>
    </div>
  </div>
  <UploadPage v-else :type="type" @goBack="showUpload = false" :myImages="myImages"/>
</template>

<script lang="ts" setup>
import { inject, ref } from "vue";
import { furnish } from "./ts/furnishStyles";
import { closeToast, showLoadingToast, showToast } from "vant";
import HomePage from "./views/HomePage.vue";
import ExamplePage from "./views/ExamplePage.vue";
import RankingPage from "./views/RankingPage.vue";
import MyPage from "./views/MyPage.vue";
import UploadPage from "./views/UploadPage.vue";

import dayjs from "dayjs";
import { BaseInfo } from "@/types/BaseInfo";
import furnishStyles from "./ts/furnishStyles";
import { httpRequest } from "utils/service";

const baseInfo = inject("baseInfo") as BaseInfo;
const decoData = inject("decoData");

const activityData = inject("activityData") as any;

const isLoadingFinish = ref(false);
const checkActTime = () => {
  const now = dayjs();
  if (now.isBefore(dayjs(baseInfo.startTime))) {
    showToast("活动未开始");
    return false;
  }
  if (now.isAfter(dayjs(baseInfo.endTime))) {
    showToast("活动已结束");
    return false;
  }
  return true;
};
// 当前显示的页面
const currentPage = ref(0);
// 当前高亮的tab
const active = ref(0);
// 切换页面
const switchPage = (pageKey: number) => {
  active.value = Number(pageKey);
  currentPage.value = Number(pageKey);
};

const iconList = [
  {
    icon: {
      active:
        "https://img10.360buyimg.com/imgzone/jfs/t1/338411/4/23148/1123/68faf3efF92483d0d/5decc5a423023b87.png",
      inactive:
        "https://img10.360buyimg.com/imgzone/jfs/t1/340119/24/26894/1455/690457b4Fc7c471c9/9b2e9bc8b83e7e49.png",
    },
  },
  {
    icon: {
      active:
        "https://img10.360buyimg.com/imgzone/jfs/t1/353906/22/427/2181/68faf3efF872e14d9/3115ba6bbbccf844.png",
      inactive:
        "https://img10.360buyimg.com/imgzone/jfs/t1/356497/26/1156/2552/6904777fF9b58d6df/a87925896623b8c3.png",
    },
  },
  {
    icon: {
      active:
        "https://img10.360buyimg.com/imgzone/jfs/t1/351369/2/17224/1445/68faf3efFff7735c6/2b27e2fd8e66e927.png",
      inactive:
        "https://img10.360buyimg.com/imgzone/jfs/t1/337948/26/26112/1665/690457b4F626c696f/1cb2fd11a019ae45.png",
    },
  },
  {
    icon: {
      active:
        "https://img10.360buyimg.com/imgzone/jfs/t1/102675/16/32378/1627/68faf3efFb504a044/13425fd25264411c.png",
      inactive:
        "https://img10.360buyimg.com/imgzone/jfs/t1/349650/38/20092/1949/690457b3Fd672e885/b6b424648fa2c945.png",
    },
  },
];

const shopName = ref("xxx自营旗舰店");

const ruleText = ref("");
const rulePopup = ref(false);
const myPrizePopup = ref(false);

const showSelect = ref(false);
const showUpload = ref(false);

// 上传按钮点击
const uploadClick = () => {
  myImages.value = {}; // 清空修改数据，表示新发布
  showUpload.value = true;
};
const type = ref("");

// 存储要修改的图片数据
const myImages = ref({});

// 我的页面修改的回调
const changeMyImages = (data: any) => {
  myImages.value = data;
  showUpload.value = true;
};

// 首页任务需要tab的切换
const operTypeEmit = (data: string) => {
  console.log(data, "首页任务需要tab的切换======");
  if (data === "taskInviteShare") {
    active.value = Number(3);
    currentPage.value = Number(3);
  } else if (data === "taskUpload") {
    type.value = "doTask";
    showUpload.value = true;
  }
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  // try {
  //   showLoadingToast({
  //     message: '加载中...',
  //     forbidClick: true,
  //     duration: 0,
  //   });
  //   await Promise.all([]);
  //   isLoadingFinish.value = true;
  //
  //   closeToast();
  //   if (!checkActTime()) {
  //     return;
  //   }
  //   if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
  //     joinPopup.value = true;
  //     console.log('您不是会员');
  //     return;
  //   }
  // } catch (error: any) {
  //   console.error(error);
  //
  //   closeToast();
  // }
};
init();
</script>

<style scoped lang="scss">
.basePage {
  padding: 0 0 1.4rem 0;
  overflow: hidden;
}
.navBar {
  width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  z-index: 90; /* 确保导航栏在最上层 */

  .tabbarBox {
    background-size: 100%;
    background-repeat: no-repeat;
    height: 1.4rem;
    padding: 0.2rem 0 0 0;
    background-color: transparent;
    .uploadIcon {
      width: 1.1rem;
      height: 1.1rem;
      position: absolute;
      bottom: 1.1rem;
      z-index: 90;
      transform: translateX(-50%);
      left: 50%;
    }
  }
}
.van-tabbar-item--active {
  background-color: transparent;
}
</style>
