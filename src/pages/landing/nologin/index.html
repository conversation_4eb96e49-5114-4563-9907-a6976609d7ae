<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>去登录</title>
</head>
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
  }



  .login-btn {
    width: 100px;
    height: 40px;
    background-color: #ff0004;
    color: #fff;
    border-radius: 5px;
    font-size: 16px;
    border: none;
  }
</style>

<body>
  <div class="container">
    <button class="login-btn">去登录</button>
  </div>
</body>
<script>
  const redirect = new URLSearchParams(window.location.search).get('redirect');
  document.querySelector('button').addEventListener('click', () => {
    console.log('button click');
    if (redirect) {
      window.location.href = redirect;
    }
  });
</script>

</html>