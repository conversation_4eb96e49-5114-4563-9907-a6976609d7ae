<template>
  <div class="kv">
    <img class="w100" src="../assets/kv.png" alt="" />
    <div class="btn-group">
      <div class="btn" @click="showPopup.isShowRulePopup = true">活动规则</div>
      <div class="btn prize-btn" @click="showPopup.isShowPrizePopup = true">我的奖品</div>
      <div class="btn photo-btn" @click="showPopup.isShowPhotosPopup = true">我的合拍</div>
    </div>
    <div class="link">
      <div @click="gotoSkuPage(rightsSkuId)"></div>
      <div @click="gotoSkuPage(appointmentSkuId)"></div>
    </div>
  </div>
  <ActRule :isShowPopup="showPopup.isShowRulePopup" @closeDialog="showPopup.isShowRulePopup = false"></ActRule>
  <MyPrize :is-show-popup="showPopup.isShowPrizePopup" @closeDialog="showPopup.isShowPrizePopup = false"></MyPrize>
  <MyPhotos :is-show-popup="showPopup.isShowPhotosPopup" @closeDialog="showPopup.isShowPhotosPopup = false"></MyPhotos>
</template>
<script lang="ts" setup>
import { gotoSkuPage } from '@/utils/platforms/jump';
import ActRule from '../components/ActRule.vue';
import MyPrize from '../components/MyPrize.vue';
import { reactive } from 'vue';
import MyPhotos from '../components/MyPhotos.vue';

const props = defineProps({
  rightsSkuId: { type: Number, required: true },
  appointmentSkuId: { type: Number, required: true },
});
const showPopup = reactive({ isShowRulePopup: false, isShowPrizePopup: false, isShowPhotosPopup: false });
</script>
<style lang="scss" scoped>
.kv {
  position: relative;
  .btn-group {
    position: absolute;
    top: 0.05rem;
    right: 0;
    .btn {
      font-family: 'OPPOSansM';
      background-image: linear-gradient(90deg, #c5d5ee 0%, #5c7496 100%);
      border-radius: 0.17rem 0 0 0.17rem;
      font-size: 0.24rem;
      color: #ffffff;
      padding: 0.09rem 0.15rem;
      box-sizing: border-box;
      line-height: 1;
      margin-bottom: 0.05rem;
    }
  }
  .link {
    position: absolute;
    bottom: 0.2rem;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    div {
      flex: 1;
      height: 1rem;
    }
  }
}
</style>
