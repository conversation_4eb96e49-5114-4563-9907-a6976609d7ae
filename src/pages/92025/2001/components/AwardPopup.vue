<template>
  <div class='winner-bg'>
    <div class='fill-address-btn' @click='saveAddress()'></div>
  </div>
</template>

<script lang='ts' setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage, gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}

const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.activityPrizeId);
};

</script>

<style scoped lang='scss'>
.winner-bg {
  height: 8.6rem;
  width: 6.5rem;
  position: relative;
  background: {
    image: url("https://img10.360buyimg.com/imgzone/jfs/t1/8169/34/24503/76885/668f9e1dFf3aaa861/216161c78319899e.png");
    repeat: no-repeat;
    size: contain;
  };

  .fill-address-btn {
    width: 3.5rem;
    height: .8rem;
    position: absolute;
    bottom: .2rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
