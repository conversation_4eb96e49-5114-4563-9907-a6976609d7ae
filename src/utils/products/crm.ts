import constant from '@/utils/constant';
import JD from '@/utils/platforms';
import { httpNoAuthRequest, httpRequest } from '@/utils/service';
import { ActivityBaseInfo } from '@/types/ActivityBaseInfo';
import { ActivityStatus } from '@/types/ActivityStatus';
import { UserInfo } from './types/UserInfo';
import { CLIENT_TYPE, getClientType } from '@/utils/platforms/clientType';
import { removeTokenFromUrl } from '@/utils/platforms/auth';
import SHARE_TYPE from '@/utils/platforms/shareType';
import { setHeaderShare } from '../platforms/share';
import { showToast } from 'vant';

interface LoginRequest {
  activityMainId: string,
  shopId: string,
  templateCode: string,
  activityType: string,
}

interface MockLoginRequest extends LoginRequest {
  mockCode: string;
}

const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';

const getServerTime = async () => {
  try {
    const { data } = await httpRequest.get('/system/time');
    sessionStorage.setItem(constant.LZ_SERVER_TIME, data);
    return data;
  } catch (e) {
    return new Date().getTime();
  }
};

const getUUID = async () => {
  if (getClientType() === CLIENT_TYPE.JDAPP) {
    try {
      const {
        status,
        data,
      } = await window.jmfe.getDeviceInfo();
      if (status === '0') {
        // data就是获取到的设备信息
        return data?.uuid;
      }
    } catch (e) {
      console.error(e);
    }
  }
  return '';
};

// ===================== 活动信息 =====================

/**
 * 获取活动信息
 */
const getActivityBaseInfo = async (): Promise<ActivityBaseInfo> => {
  const res = await httpRequest.post('/common/getActivityBase');
  console.log('getActivityBaseInfo.data', res);
  return res.data;
};

/**
 * 获取活动状态
 * @param baseInfo
 * @returns 0:
 */
const getActivityStatus = (baseInfo: ActivityBaseInfo): ActivityStatus => {
  if (baseInfo.endTime === 0 || baseInfo.startTime === 0) {
    return ActivityStatus.normal;
  }
  if (baseInfo.status === -1) {
    return ActivityStatus.noexit;
  }
  const time = new Date().getTime();
  if (time > baseInfo.endTime) {
    return ActivityStatus.finish;
  }
  if (time < baseInfo.startTime) {
    console.log('🚀时间差🚀', baseInfo.startTime - time);
    const delay = baseInfo.startTime - time > 3600000 ? 3600000 : baseInfo.startTime - time;
    setTimeout(() => {
      window.location.reload();
    }, delay);
    return ActivityStatus.notStarted;
  }
  return ActivityStatus.normal;
};

/**
 * 获取pin
 * @param mockLoginRequest
 */
const mockLogin = async (mockLoginRequest: MockLoginRequest): Promise<UserInfo> => {
  const uuid = await getUUID();
  const { data } = await httpNoAuthRequest.post('/user/mockLogin', {
    code: mockLoginRequest.mockCode,
    activityMainId: mockLoginRequest.activityMainId,
    shopId: mockLoginRequest.shopId,
    activityType: mockLoginRequest.activityType,
    templateCode: mockLoginRequest.templateCode,
    uuid,
    timestamp: new Date().getTime(),
  });
  if (isDev) {
    console.table(data);
  }
  sessionStorage.setItem(constant.LZ_PIN_TOKEN, data.pinToken);
  sessionStorage.setItem(constant.LZ_JD_ENCRYPT_PIN, data.encryptPin);
  sessionStorage.setItem(constant.LZ_JD_USER_NAME, data.nickname!);
  sessionStorage.setItem(constant.LZ_JD_USER_AVATAR, data.avatar!);
  sessionStorage.setItem(constant.LZ_WHITE_USER, `${data.isWhiteUser}`);
  removeTokenFromUrl();
  return data;
};

// 用户信息

/**
 * 获取pin
 * @param loginRequest
 */
const getJdPin = async (loginRequest: LoginRequest): Promise<UserInfo> => {

  // 获取getPin所需参数
  const params = await JD.getToken();
  sessionStorage.setItem(constant.LZ_JD_TOKEN, params.token);

  // 2. 获取UUID
  const uuid = await getUUID();
  console.log('登陆数据', {
    token: params.token,
    source: params.source,
    activityMainId: loginRequest.activityMainId,
    shopId: loginRequest.shopId,
    uuid,
    timestamp: new Date().getTime(),
  });
  const {
    code,
    data,
  } = await httpNoAuthRequest.post('/user/login', {
    token: params.token,
    source: params.source,
    activityMainId: loginRequest.activityMainId,
    shopId: loginRequest.shopId,
    activityType: loginRequest.activityType,
    templateCode: loginRequest.templateCode,
    uuid,
    timestamp: new Date().getTime(),
  });
  return data;
};

/**
 * 获取PING
 * @param loginRequest
 */
const getPin = async (loginRequest: LoginRequest): Promise<UserInfo> => {

  // 用户pin
  const pinToken = sessionStorage.getItem(constant.LZ_PIN_TOKEN);
  const encryptPin = sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN);

  // 如果缓存中有pin就不要再去获取了;
  // if (pinToken && encryptPin) {
  //   return {
  //     encryptPin,
  //     pinToken,
  //   };
  // }

  const pinResponse: UserInfo = await getJdPin(loginRequest);

  if (isDev) {
    console.table(pinResponse);
  }

  sessionStorage.setItem(constant.LZ_PIN_TOKEN, pinResponse.pinToken);
  sessionStorage.setItem(constant.LZ_JD_ENCRYPT_PIN, pinResponse.encryptPin);
  sessionStorage.setItem(constant.LZ_JD_USER_NAME, pinResponse.nickname!);
  sessionStorage.setItem(constant.LZ_JD_USER_AVATAR, pinResponse.avatar!);
  sessionStorage.setItem(constant.LZ_WHITE_USER, `${pinResponse.isWhiteUser}`);

  return pinResponse;
};

// ===================== 分享 =====================

/**
 * 获取全局配置
 */
const getGlobalSetting = async (shopId: any, activityId: any): Promise<KeyValue> => {
  try {
    const res = await httpRequest.get('/global/getGlobalSetting', {
      params: {
        shopId,
        activityId,
      },
    });
    console.log('getGlobalSetting.data', res);
    if (res.data) {
      return res.data;
    }
    return { supportClientType: ['jd', 'jdlittle', 'jdh', 'wx', 'qq', 'weibo', 'mp', 'pc', 'm', 'other'] };
  } catch (e) {
    console.error(e);
    return { supportClientType: ['jd', 'jdlittle', 'jdh', 'wx', 'qq', 'weibo', 'mp', 'pc', 'm', 'other'] };
  }
};

/**
 * 设置京口令/H5页面数据信息
 * false:京口令
 * true:H5
 * @return
 */
const getShareConfig = async (query: any): Promise<any> => {
  const { data } = await httpRequest.post('/common/getShareConfig', query);
  return data;
};

// 获取当前模板的配置数据
const getConfig = async (response: { id: string; type: string }) => {
  if (response.type) {
    const { data } = await httpRequest.post('/preview/getConfig', response);
    showToast('活动预览，仅供查看');
    return data;
  }
  return {
    activityData: '',
    decoData: '',
  };

};

// // 分享拦截器
// async function shareInterceptor(shareType: string, activityId?: string) {
//   if (isProd) {
//     if (
//       shareType === JD.SHARE_TYPE.MINIAPP
//       && JD.getClientType() === JD.CLIENT_TYPE.WECHAT
//     ) {
//       const isMiniProgram = await JD.isMiniProgram();
//       console.log('isMiniProgram', isMiniProgram);
//       // 并且不是微信的小程序里面
//       if (!isMiniProgram) {
//         console.log('小程序落地页');
//         window.location.href = `https://lzkjdz-isv.isvjcloud.com/miniProInfo/indexPage/?activityId=${activityId}&mpUrl=${encodeURIComponent(
//           window.location.href,
//         )}`;
//         throw Error('小程序落地页');
//       }
//     } else if (
//       shareType === JD.SHARE_TYPE.CMD
//       && JD.getClientType() === JD.CLIENT_TYPE.WECHAT
//     ) {// 分享拦截器
// // async function shareInterceptor(shareType: string, activityId?: string) {
// //   if (isProd) {
// //     if (
// //       shareType === JD.SHARE_TYPE.MINIAPP
// //       && JD.getClientType() === JD.CLIENT_TYPE.WECHAT
// //     ) {
// //       const isMiniProgram = await JD.isMiniProgram();
// //       console.log('isMiniProgram', isMiniProgram);
// //       // 并且不是微信的小程序里面
// //       if (!isMiniProgram) {
// //         console.log('小程序落地页');
// //         window.location.href = `https://lzkjdz-isv.isvjcloud.com/miniProInfo/indexPage/?activityId=${activityId}&mpUrl=${encodeURIComponent(
// //           window.location.href,
// //         )}`;
// //         throw Error('小程序落地页');
// //       }
// //     } else if (
// //       shareType === JD.SHARE_TYPE.CMD
// //       && JD.getClientType() === JD.CLIENT_TYPE.WECHAT
// //     ) {
// //       console.log('H5落地页');
// //       window.location.href = `https://lzkjdz-isv.isvjcloud.com/miniProInfo/indexPageDzNew/${activityId}/?activityId=${activityId}&mpUrl=${encodeURIComponent(
// //         window.location.href,
// //       )}`;
// //       throw Error('H5落地页');
// //     }
// //   }
// // }
//       console.log('H5落地页');
//       window.location.href = `https://lzkjdz-isv.isvjcloud.com/miniProInfo/indexPageDzNew/${activityId}/?activityId=${activityId}&mpUrl=${encodeURIComponent(
//         window.location.href,
//       )}`;
//       throw Error('H5落地页');
//     }
//   }
// }

// 初始化分享配置
const initShare = async (shareParams?: Record<string, any>, activityId?: string, pathParams?: { [propName: string]: string }) => {

  let shareUrl = window.location.href;

  // 如果是分享出去的链接，去掉shareId参数，保留其他参数
  if (pathParams?.shareId) {
    const url = new URL(shareUrl);
    url.searchParams.delete('shareId');
    shareUrl = url.toString();
  }

  const shareConfig = await getShareConfig({
    url: shareUrl,
    // 可以扩展分享的时候传递的参数
    extra: {
      adSource: pathParams?.adSource,
      ...shareParams,
    },
  });

  // 分享方式（0:京口令，1:H5，2:小程序）
  const shareType = shareConfig.shareType ?? SHARE_TYPE.CMD;

  // 判断shareImage是否包含http，如果不包含添加https:
  if (shareConfig.shareImage && !shareConfig.shareImage.includes('http')) {
    shareConfig.shareImage = `https:${shareConfig.shareImage}`;
  }

  // // 缓存分享方式
  window.sessionStorage.setItem(constant.LZ_SHARE_CONFIG, JSON.stringify(shareConfig));
  window.sessionStorage.setItem(constant.LZ_SHARE_TYPE, shareType);

  !shareConfig.disabled && await setHeaderShare({
    debug: !!pathParams?.debug,
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
    // 分享落地页
    shareUrl: `${process.env.VUE_APP_HOST}landing/share/?shareId=${shareConfig.shareId}`,
  });

  // 分享拦截器
  // await shareInterceptor(shareType, activityId);
};

// 获取大促公告
const getNotice = async () => {
  try {
    const { data } = await httpRequest.post('/common/getNotice');
    if (data && data.context) {
      const { context } = data;
      // 创建跑马灯元素
      const marquee = document.createElement('div');
      marquee.className = 'notice-marquee';
      const marqueeContext = document.createElement('div');
      marqueeContext.className = 'notice-marquee-context';
      marqueeContext.innerHTML = context;
      marquee.appendChild(marqueeContext);
      // 马灯元素插入body第一行中
      document.body.insertBefore(marquee, document.body.firstChild);
    }
  } catch (error: any) {
    console.error(error);
  }
};

/**
 * 获取装修数据
 */
const getActivityConfig = async () => {
  const { data } = await httpRequest.post('/common/getActivityConfig');
  return JSON.parse(data);
};

export default {
  mockLogin,
  getPin,
  getServerTime,
  getActivityBaseInfo,
  getActivityStatus,
  getConfig,
  getActivityConfig,
  initShare,
  getNotice,
  getGlobalSetting,
};
