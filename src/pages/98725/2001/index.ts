import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import './index.scss';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';

initRem();
const app = createApp(root);

const getDecoData = async () => {
  try {
    const { data } = await httpRequest.post('common/getActivityConfig');
    app.provide('decoData', JSON.parse(data));
  } catch (error: any) {
    console.error(error);
  }
};

// 初始化页面
const config: InitRequest = {
  urlPattern: '/custom/:activityType/:templateCode',
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  showUnStartPage: false,
  showFinishedPage: false,
};
init(config).then(async ({ baseInfo, pathParams }) => {
  // 设置页面title
  await getDecoData();
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
