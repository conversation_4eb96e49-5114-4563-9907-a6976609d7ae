import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import CLIENT_TYPE, { getClientType } from '@/utils/platforms/clientType';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  disableShare: true,
  shopId: '1000013402',
  activityMainId: '1928291050117734402',
};

const isWechat = getClientType() === CLIENT_TYPE.WECHAT;
if (isWechat) {
  const accessUrl = encodeURIComponent(window.location.href);
  window.location.href = `https://lzkjdz-isv.isvjcloud.com/prod/cc/custom/landing/openAppPage2/?actlink=${accessUrl}`;
} else {
  init(config).then(({ baseInfo, pathParams }) => {
    // 设置页面title
    document.title = '高考搭子选伊利';
    app.provide('baseInfo', baseInfo);
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, {});
    app.mount('#app');
  });
}
// app.mount('#app');
