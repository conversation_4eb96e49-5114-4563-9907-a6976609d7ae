import { createApp } from 'vue';
import root from './App.vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();
const app = createApp(root);
// 初始化页面
const config: InitRequest = {
  disableShare: true,
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  disableDecorate: true,
  disableThreshold: true,
};
init(config)
  .then(({ baseInfo, pathParams }) => {
    localStorage.removeItem('browseTaskId');
    // 设置页面title
    document.title = baseInfo?.activityName || '';
    app.provide('pathParams', pathParams);
    app.provide('baseInfo', baseInfo);
    app.mount('#app');
  })
  .catch((e) => {
    console.error(e);
  });
