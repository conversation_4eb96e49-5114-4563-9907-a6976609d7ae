// 页面初始化的配置
export interface InitRequest {
  // 路径模式
  urlPattern?: string;

  // 手动指定shopId
  shopId?: string;

  // 手动指定活动id
  activityMainId?: string;

  // 活动门槛弹窗组件
  thresholdPopup?: any;

  // 禁用门槛弹窗默认为false
  disableThresholdPopup?: boolean;

  // 是否分享
  disableShare?: boolean;

  // 回到页面是否刷新页面
  backActRefresh?: boolean;

  // 禁用全局提示
  disableNotice?: boolean;

  // 分享参数
  shareParams?: Record<string, any>;

  // 活动类型
  activityType?: string;

  // 是否显示未开始页面
  showUnStartPage?: boolean;

  // 是否显示活动结束页面
  showFinishedPage?: boolean;

  loginType?: number;

  templateCode?: string;
  /** 不需要加载装修数据 */
  disableDecorate?: boolean;
  /** 不校验活动门槛 */
  disableThreshold?: boolean;
  // 兜底页链接
  errorPageUrl?: string;
}
