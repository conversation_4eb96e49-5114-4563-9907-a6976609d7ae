import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import App from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import CLIENT_TYPE, { getClientType } from '@/utils/platforms/clientType';
import '@/style';

initRem();

const app = createApp(App);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  disableShare: true,
  // showUnStartPage: true,
};

// const clientType = getClientType();

// const removeTokenFromUrl = (url: string): string => {
//   try {
//     const urlObj = new URL(url);
//     urlObj.searchParams.delete('token');
//     return urlObj.toString();
//   } catch (error) {
//     console.error('URL解析错误:', error);
//     return url;
//   }
// };
// if (clientType === CLIENT_TYPE.WECHAT) {
//   const accessUrl = encodeURIComponent(window.location.href.split('#')[0]);
//   window.localStorage.setItem('actlink', accessUrl);
//   const result = removeTokenFromUrl(accessUrl);
//   window.location.href = `${process.env.VUE_APP_HOST}landing/openAppPage2/?url=${result}`;
// }

init(config).then(({ baseInfo, pathParams, userInfo }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('baseUserInfo', userInfo);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
