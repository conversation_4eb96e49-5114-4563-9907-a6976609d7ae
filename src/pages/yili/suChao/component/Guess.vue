<template>
  <div class="guess">
    <div class="record" @click="recordPop = true"></div>
    <div class="guess-num">剩余竞猜机会：{{ userInfo.guessNum }}次</div>
    <div class="guess-date">赛事日期：{{ guessList[activeIndex] ? dayjs(guessList[activeIndex].competitionTime).format('YYYY-MM-DD') : '' }}</div>
    <div class="guess-swiper swiper-container" v-if="guessList.length > 0">
      <div class="swiper-wrapper">
        <div class="guess-item swiper-slide" v-for="(item, index) in guessList" :key="index">
          <div class="team-flex">
            <div class="team-box">
              <img :src="item.teamOneImg" alt="" />
              <img class="left-icon" src="../assets/winIcon.png" alt="" v-if="item.competitionResult === 1" />
              <img class="left-icon" src="../assets/noWinIcon.png" alt="" v-else-if="item.competitionResult === 2" />
              <img class="left-icon" src="../assets/drawIcon.png" alt="" v-else-if="item.competitionResult === 0" />
            </div>
            <img src="../assets/VS.png" alt="" class="vs" />
            <div class="team-box">
              <img :src="item.teamTwoImg" alt="" />
              <img class="right-icon" src="../assets/winIcon.png" alt="" v-if="item.competitionResult === 2" />
              <img class="right-icon" src="../assets/noWinIcon.png" alt="" v-else-if="item.competitionResult === 1" />
              <img class="right-icon" src="../assets/drawIcon.png" alt="" v-else-if="item.competitionResult === 0" />
            </div>
          </div>
          <div class="btn-list">
            <div class="win-btn" :class="{ 'win-btn-gray': checkGuessBtn(item, 1) }" @click="selectGuess(item, 1)">
              {{ item.teamOneName }}胜
              <img class="user-icon" src="../assets/userGuess.png" alt="" v-if="item.guessType === 1" />
            </div>
            <div class="draw-btn" :class="{ 'win-btn-gray': checkGuessBtn(item, 0) }" v-if="item.guessOptions.includes('0')" @click="selectGuess(item, 0)">
              平局
              <img class="user-icon" src="../assets/userGuess.png" alt="" v-if="item.guessType === 0" />
            </div>
            <div class="win-btn" :class="{ 'win-btn-gray': checkGuessBtn(item, 2) }" @click="selectGuess(item, 2)">
              {{ item.teamTwoName }}胜
              <img class="user-icon" src="../assets/userGuess.png" alt="" v-if="item.guessType === 2" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="guessList.length === 0" class="no-data">当前无可竞猜赛事</div>
    <div class="guess-rule">
      消耗竞猜机会支持球队，每场赛事限1次竞猜<br />
      每猜中1场，获得{{ guessPrize?.prizeName }}奖励
    </div>
    <div>
      <img src="../assets/leftBtn.png" alt="" class="prev" @click="prev" />
      <img src="../assets/leftBtn.png" alt="" class="next" @click="next" />
    </div>
    <div class="prize-content">
      <div class="prize-box">
        <div class="title">
          活动期间累计猜中{{ guessMatchPrize?.guessNum }}场<br />
          将解锁{{ guessMatchPrize?.prizeName }}
        </div>
        <div class="prize-img">
          <img :src="guessMatchPrize?.prizeImg" alt="" />
        </div>
        <div class="prize-btn" :class="{ gary: !guessMatchPrize?.userCanWin || guessMatchPrize?.isWin || guessMatchPrize?.remainNum <= 0 }" @click="getPrize(guessMatchPrize, 4)">
          <span v-if="guessMatchPrize?.userCanWin && !guessMatchPrize?.isWin && guessMatchPrize?.remainNum > 0">已解锁</span>
          <span v-else-if="guessMatchPrize?.isWin">已领取</span>
          <span v-else-if="guessMatchPrize?.remainNum <= 0">已兑完</span>
          <span v-else-if="!guessMatchPrize?.userCanWin">未解锁</span>
        </div>
      </div>
      <div class="prize-box">
        <div class="title">
          活动期间猜中{{ guessMatchConsumePrize?.guessNum }}场且累计消费最高<br />
          有机会得{{ guessMatchConsumePrize?.prizeName }}
        </div>
        <div class="prize-img">
          <img :src="guessMatchConsumePrize?.prizeImg" alt="" />
        </div>
        <div class="prize-btn" :class="{ gary: !guessMatchConsumePrize?.userCanWin || guessMatchConsumePrize?.isWin || guessMatchConsumePrize?.remainNum <= 0 }" @click="getPrize(guessMatchConsumePrize, 5)">
          <span v-if="guessMatchConsumePrize?.userCanWin && !guessMatchConsumePrize?.isWin && guessMatchConsumePrize?.remainNum > 0">已解锁</span>
          <span v-else-if="guessMatchConsumePrize?.isWin">已领取</span>
          <span v-else-if="guessMatchConsumePrize?.remainNum <= 0">已兑完</span>
          <span v-else-if="!guessMatchConsumePrize?.userCanWin">未解锁</span>
        </div>
      </div>
    </div>

    <VanPopup teleport="body" v-model:show="recordPop">
      <GuessRecord v-if="recordPop" @close="recordPop = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="confirmPop">
      <ConfirmTeam :teamIcon="selectTeam.teamImg" v-if="confirmPop" @close="confirmPop = false" @confirm="confirmTeam" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="receivedSuccessfullyPop">
      <ReceivedSuccessfully :prizeInfo="prizeInfo" v-if="receivedSuccessfullyPop" @close="receivedSuccessfullyPop = false" @saveAddress="toSaveAddress" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="saveAddressPop">
      <SaveAddress v-if="saveAddressPop" :address-id="prizeInfo.addressId" @close="saveAddressPop = false" @success="saveAddressPop = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { guessList, userInfo, Guess, guessMatchPrizeList, guessMatchConsumePrizeList, Prize, getGuessList, getUserInfo, getPrizeList, guessPrizeList } from '../hooks';
import GuessRecord from '../Pop/GuessRecord.vue';
import ConfirmTeam from '../Pop/ConfirmTeam.vue';
import ReceivedSuccessfully from '../Pop/ReceivedSuccessfully.vue';
import SaveAddress from '../Pop/SaveAddress.vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const guessPrize = computed(() => guessPrizeList.value[0]);
const guessMatchPrize = computed(() => guessMatchPrizeList.value[0]);
const guessMatchConsumePrize = computed(() => guessMatchConsumePrizeList.value[0]);

const recordPop = ref(false);
const confirmPop = ref(false);
const receivedSuccessfullyPop = ref(false);
const saveAddressPop = ref(false);

const prizeInfo = ref<any>({});

const checkGuessBtn = (item: any, guessType: number) => {
  // 判断当前时间是否在开赛时间之前
  const isBeforeCompetition = dayjs().isBefore(dayjs(item.competitionTime));

  // 判断用户是否已竞猜 (guessType: 3-未竞猜, 0|1|2-已竞猜)
  const hasGuessed = item.guessType !== 3;

  if (isBeforeCompetition) {
    // 开赛前
    if (hasGuessed) {
      // 已竞猜：全部置灰
      return true;
    } else {
      // 未竞猜：判断用户是否还有竞猜次数
      if (userInfo.value.guessNum <= 0) {
        return true;
      }
      // 未竞猜：全部高亮
      return false;
    }
  } else {
    // 开赛后
    // 全部置灰
    return true;
  }
};
const selectTeam = ref({
  guessId: 0,
  guessType: 0,
  teamImg: '',
});
const selectGuess = (item: Guess, guessType: number) => {
  if (dayjs().isAfter(dayjs(item.competitionTime))) {
    showToast('竞猜已截止');
    return;
  }
  if (item.guessType !== 3) {
    showToast('您已竞猜过');
    return;
  }
  if (userInfo.value.guessNum <= 0) {
    showToast('您的竞猜次数已用完');
    return;
  }

  selectTeam.value.guessId = item.guessId;
  selectTeam.value.guessType = guessType;
  if (guessType === 1) {
    selectTeam.value.teamImg = item.teamOneImg;
  } else if (guessType === 2) {
    selectTeam.value.teamImg = item.teamTwoImg;
  } else {
    selectTeam.value.teamImg = 'https://img10.360buyimg.com/imgzone/jfs/t1/314880/12/18895/77131/687f8e34F5ae0c8c2/b44bc8e0b9c8616d.png';
  }
  confirmPop.value = true;
};

const confirmTeam = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/brand/yiLiSuChao/userGuess', {
      guessId: selectTeam.value.guessId,
      guessType: selectTeam.value.guessType,
    });
    await getGuessList();
    closeToast();
    showToast('提交成功');
    confirmPop.value = false;
    getUserInfo();
    getPrizeList(4);
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

const api: Record<number, string> = {
  4: '/brand/yiLiSuChao/receivePeripheralProducts',
  5: '/brand/yiLiSuChao/userHighestGuess',
};

const getPrize = async (item: Prize, type: number) => {
  try {
    console.log(item);
    if (item.remainNum <= 0) {
      return;
    }
    if (!item.userCanWin) {
      return;
    }
    if (item.isWin) {
      return;
    }
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post(api[type], {
      prizeId: item.prizeId,
    });
    closeToast();
    getPrizeList(type);
    if (data.status === 1) {
      prizeInfo.value = data;
      receivedSuccessfullyPop.value = true;
    } else {
      showToast('领取失败');
    }
  } catch (error: any) {
    getPrizeList(type);
    closeToast();
    showToast(error.message);
    console.log(error);
  }
};

const toSaveAddress = () => {
  receivedSuccessfullyPop.value = false;
  saveAddressPop.value = true;
};

let guessSwiper: Swiper;
const activeIndex = ref(0);
const prev = () => {
  guessSwiper.slidePrev();
};
const next = () => {
  guessSwiper.slideNext();
};
const initSwiper = () => {
  nextTick(() => {
    if (guessSwiper) {
      guessSwiper.destroy();
    }
    activeIndex.value = 0;
    guessSwiper = new Swiper('.guess-swiper', {
      slidesPerView: 1,
      spaceBetween: 0,
      // 当前轮播居中
      centeredSlides: true,
      on: {
        slideChange: () => {
          activeIndex.value = guessSwiper.activeIndex;
        },
      },
    });
  });
};
watch(
  () => guessList,
  () => {
    initSwiper();
  },
);
onMounted(() => {
  initSwiper();
});
</script>

<style scoped lang="scss">
.guess {
  position: relative;
  width: 7.35rem;
  height: 12.41rem;
  background: url('../assets/act3.png') no-repeat;
  background-size: 100%;
  margin: 0 auto 0.25rem;
  padding-top: 1.47rem;
  .record {
    position: absolute;
    top: 0;
    right: 0;
    width: 1.47rem;
    height: 0.48rem;
  }
  .guess-num {
    font-size: 0.32rem;
    color: #fff;
    line-height: 0.32rem;
    font-family: 'FZY4JW';
    text-align: center;
    height: 0.32rem;
    margin-bottom: 0.4rem;
  }
  .guess-date {
    font-size: 0.32rem;
    color: #fff;
    line-height: 0.54rem;
    font-family: 'FZY4JW';
    text-align: center;
    height: 0.54rem;
    margin-bottom: 0.1rem;
  }
  .guess-swiper {
    width: 7.2rem;
    height: 3.56rem;
    overflow: hidden;
    padding-top: 0.2rem;
    .team-flex {
      display: flex;
      justify-content: center;
      align-items: center;
      .team-box {
        position: relative;
        width: 2.5rem;
        height: 2.5rem;
        background: url('../assets/teamBg.png') no-repeat;
        background-size: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 1.85rem;
        }
        .left-icon {
          position: absolute;
          top: -0.2rem;
          left: -0.4rem;
          width: 0.79rem;
        }
        .right-icon {
          position: absolute;
          top: -0.2rem;
          right: -0.4rem;
          width: 0.79rem;
        }
      }
      .vs {
        width: 0.7rem;
        margin: 0 0.12rem;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 0.34rem 0.3rem 0;
      .win-btn {
        position: relative;
        width: 1.97rem;
        height: 0.52rem;
        background: url('../assets/winBtn.png') no-repeat;
        background-size: 100%;
        font-size: 0.3rem;
        color: #fff;
        line-height: 0.52rem;
        font-family: 'XiaoxinChaoku';
        text-align: center;
      }
      .draw-btn {
        position: relative;
        width: 1.97rem;
        height: 0.52rem;
        background: url('../assets/drawBtn.png') no-repeat;
        background-size: 100%;
        font-size: 0.3rem;
        color: #fff;
        line-height: 0.52rem;
        font-family: 'XiaoxinChaoku';
        text-align: center;
      }
      .win-btn-gray {
        background: url('../assets/winBtngray.png') no-repeat;
        background-size: 100%;
      }
      .user-icon {
        position: absolute;
        top: -0.2rem;
        right: -0.2rem;
        width: 0.47rem;
        height: 0.47rem;
      }
    }
  }
  .no-data {
    text-align: center;
    height: 3.56rem;
    line-height: 3.56rem;
    font-size: 0.3rem;
    color: #fff;
    font-family: 'FZY4JW';
  }
  .guess-rule {
    padding-top: 0.25rem;
    font-size: 0.3rem;
    color: #fff;
    line-height: 0.34rem;
    font-family: 'FZY4JW';
    text-align: center;
  }
  .prev {
    width: 0.49rem;
    position: absolute;
    top: 3.78rem;
    left: -0.125rem;
    z-index: 10;
  }
  .next {
    width: 0.49rem;
    position: absolute;
    top: 3.78rem;
    right: -0.07rem;
    transform: rotate(180deg);
    z-index: 10;
  }
  .prize-content {
    display: flex;
    margin-top: 0.8rem;
    .prize-box {
      flex: 0.5;
      .title {
        font-size: 0.22rem;
        color: #fff;
        line-height: 0.28rem;
        font-family: 'FZY4JW';
        text-align: center;
        letter-spacing: -0.019rem;
      }
      .prize-img {
        width: 2.5rem;
        height: 2.5rem;
        background: url('../assets/teamBg.png') no-repeat;
        background-size: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0.1rem auto 0.2rem;
        img {
          width: 2.36rem;
          height: 2.36rem;
          object-fit: contain;
          border-radius: 0.25rem;
        }
      }
      .prize-btn {
        width: 1.97rem;
        height: 0.53rem;
        background: url('../assets/unlockedBtn.png') no-repeat;
        background-size: 100%;
        margin: 0 auto;
        text-align: center;
        font-size: 0.3rem;
        line-height: 0.53rem;
        color: #fff;
        font-family: 'XiaoxinChaoku';
        img {
          width: 100%;
        }
      }
      .gary {
        filter: grayscale(1);
      }
    }
  }
}
</style>
