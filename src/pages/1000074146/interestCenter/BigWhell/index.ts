import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, initPreview } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';
import { convertStringsToJSON, preview } from '../Utils';
import { decorationInfo, popupDecorationInfo } from './DataHooks';
import { prizeInfo } from './components/Info';
import IAmorLottery from 'iamor-lottery-vue';

initRem();
const notJoinPageUrl = `${process.env.VUE_APP_HOST}1000074146/interestCenter/notJoinPage/`;
const app = createApp(root);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  activityMainId: '1830542371332141058',
  shopId: '1000074146',
  activityType: '2',
};

const upDataPrize = async () => {
  try {
    const { data } = await httpRequest.post('/swisseDraw/drawPrize');
    prizeInfo.forEach((item: any, index: number) => {
      const prize = data.find((it: any) => it.sortId === index + 1);
      if (prize) {
        prizeInfo[index] = prize;
      }
    });
  } catch (error: any) {
    console.error(error);
  }
};

const getDecorationData = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/decorationData', {
      pageType: '5',
    });
    const resultMap: any = {};
    data.forEach((item: any) => {
      resultMap[item.keyName] = item.decorationData;
    });
    decorationInfo.value = convertStringsToJSON(resultMap);
  } catch (error: any) {
    console.error(error);
  }
};
const getPopupDeco = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/decorationData', {
      pageType: '4',
    });
    const resultMap: any = {};
    data.forEach((item: any) => {
      resultMap[item.keyName] = item.decorationData;
    });
    popupDecorationInfo.value = convertStringsToJSON(resultMap);
    popupDecorationInfo.value.thresholdPage.saveAddressAfter = popupDecorationInfo.value.thresholdPage.saveAddress || {};
  } catch (error: any) {
    console.error(error);
  }
};
// 获取会员类型
const getMemberInfo = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/memberInfo');
    if (data.vipLevel > 0) {
      app.provide('memberInfo', data);
      await Promise.all([getDecorationData(), getPopupDeco()]);
      await upDataPrize();
    } else {
      window.location.href = notJoinPageUrl;
    }
  } catch (error: any) {
    window.location.href = notJoinPageUrl;
  }
};
if (preview) {
  initPreview(config).then(async () => {
    app.provide('memberInfo', { vipLevel: 1 });
    app.mount('#app');
  });
} else {
  init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
    await getMemberInfo();
    document.title = baseInfo.activityName;
    app.provide('baseInfo', baseInfo);
    app.provide('userInfo', userInfo);
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, {});
    app.mount('#app');
  });
}
