import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, initPreview } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';
import { convertStringsToJSON, preview } from '../Utils';
import { checkMemberInfo, decorationInfo, popupDecorationInfo } from './DataHooks';

initRem();

const app = createApp(root);

const hasJoinPageUrl = `${process.env.VUE_APP_HOST}1000074146/interestCenter/hasJoinPage/`;
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  activityMainId: '1830542371332141058',
  shopId: '1000074146',
  activityType: '1',
};
const getDecorationData = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/decorationData', {
      pageType: '1',
    });
    const resultMap: any = {};
    data.forEach((item: any) => {
      resultMap[item.keyName] = item.decorationData;
    });
    decorationInfo.value = convertStringsToJSON(resultMap);
  } catch (error: any) {
    console.error(error);
  }
};
const getPopupDeco = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/decorationData', {
      pageType: '4',
    });
    const resultMap: any = {};
    data.forEach((item: any) => {
      resultMap[item.keyName] = item.decorationData;
    });
    popupDecorationInfo.value = convertStringsToJSON(resultMap);
    popupDecorationInfo.value.thresholdPage.saveAddressAfter = popupDecorationInfo.value.thresholdPage.saveAddress || {};
  } catch (error: any) {
    console.error(error);
  }
};
// 获取会员类型
const getMemberInfo = async (adsource: string) => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/memberInfo');
    if (data.vipLevel < 1) {
      app.provide('memberInfo', data);
    } else {
      window.location.href = `${hasJoinPageUrl}?adsource=${adsource}`;
    }
  } catch (error: any) {
    // window.location.href = hasJoinPageUrl;
  }
};

const checkMember = async () => {
  try {
    const { data } = await httpRequest.post('/swisseNoMember/checkMember');
    checkMemberInfo.value = data;
  } catch (error: any) {
    console.error(error);
  }
};
// 获取会员类型

if (preview) {
  initPreview(config).then(async () => {
    app.provide('memberInfo', { vipLevel: 0 });
    checkMemberInfo.value = { black: 2 };
    app.mount('#app');
  });
} else {
  init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
    await getMemberInfo(pathParams?.adsource ?? 'default');
    await Promise.all([getDecorationData(), getPopupDeco()]);
    await checkMember();
    document.title = baseInfo.activityName;
    app.provide('baseInfo', baseInfo);
    app.provide('userInfo', userInfo);
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, {});
    app.mount('#app');
  });
}
