<template>
  <div
    class="main"
    v-if="isLoadingFinish"
    :style="{
      backgroundColor: `${decoData.bgColor}`,
    }"
  >
    <!-- 0 无法补领 -->
    <div class="mainStatueDiv mainStatueDiv0" v-if="pageStatus === 0">
      <div class="kv">
        <img :src="decoData.bgImage0" alt="" />
      </div>
      <div class="bottomDiv">
        <div
          class="skuDiv"
          :style="{
            backgroundImage: `url(${decoData.skuBg})`,
            height: `${decoData.skuBgLeight}`,
          }"
          @click="gotoShopPage(baseInfo.shopId)"
        >
          <div
            class="skuItemDiv"
            v-for="(item, index) in decoData.skuList"
            :key="index"
            @click="gotoShopPage(baseInfo.shopId)"
          >
            <div class="buyBtnDiv"></div>
          </div>
        </div>
        <div
          class="goToShopDiv0"
          :style="{
            backgroundImage: `url(${decoData.goToshop0})`,
          }"
          @click="gotoShopPage(baseInfo.shopId)"
        ></div>
      </div>
    </div>
    <!-- 2 已经补领 -->
    <div class="mainStatueDiv" v-else-if="pageStatus === 2">
      <div class="kv">
        <img :src="decoData.bgImage2" alt="" />
      </div>
      <div class="sendPointsDiv">
        成功补领{{ mainInfo && mainInfo.sendPoints ? mainInfo.sendPoints : 0 }}积分
      </div>
      <div class="goToShopDiv" @click="gotoShopPage(baseInfo.shopId)"></div>
    </div>

    <!-- // 1-可以补领 -->
    <div class="mainStatueDiv" v-else>
      <div class="kv">
        <img :src="decoData.bgImage1" alt="" />
      </div>
      <div class="getDrawDiv" @click="getDrawClick()"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from "vue";
import { httpRequest } from "@/utils/service";
import { BaseInfo } from "@/types/BaseInfo";
import { closeToast, showLoadingToast, showToast } from "vant";
import { gotoShopPage, toCustomerService } from "@/utils/platforms/jump";
import { lzReportClick } from "@/utils/trackEvent/lzReport";
import useThreshold from "@/hooks/useThreshold";
import openCard from "@/utils/openCard";

const isLoadingFinish = ref(false);
const decoData = inject("decoData") as any;
console.log(decoData, "装修数据==========");
const baseInfo = inject("baseInfo") as BaseInfo;
const mainInfo = ref<any>(null);
const showLimit = ref(false); // 展示门槛显示弹框
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
const pageStatus = ref(1); // 0 无法补领 2 已经补领  1 其他
const getMainInfo = async () => {
  try {
    const { data } = await httpRequest.post("/dz1983860741651091458/init");
    console.log(data, "data==========");
    if (data.actorStatus === 2) {
      // 已经补领过直接进入无法补领的页面
      pageStatus.value = 0;
    }
    // data.actorStatus = 2;
    // actorStatus 用户状态 0-无法补领 1-可以补领 2-已补领
    // openStatus  开卡状态 0-未开卡 1-开卡
    // sendPoints 发放积分
    mainInfo.value = data;
  } catch (error: any) {
    showToast(error.message);
  }
};
// 补领积分
const getDrawClick = async () => {
  if (mainInfo.value && mainInfo.value.openStatus === 0) {
    // 未开卡 需要半屏开卡
    lzReportClick("join");
    openCard(
      `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(
        `${window.location.href}&isJoin=1`
      )}`
    );
    return;
  }
  if (mainInfo.value.actorStatus === 0) {
    // 无法补领进入无法补领页面
    pageStatus.value = 0;
    return;
  }
  try {
    pageStatus.value = 1;
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post("/dz1983860741651091458/sendPoints");
    console.log(data, "data==========");
    closeToast();
    // 补领成功 进入补领成功页面
    pageStatus.value = 2;
    // await getMainInfo();
  } catch (error: any) {
    // pageStatus.value = 2;
    // closeToast();
    showToast(error.message);
  }
};
const init = async () => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([await getMainInfo()]);
    isLoadingFinish.value = true;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>
<style>
@font-face {
  font-family: "HYYakuHei-GEW";
  src: url("../1983860741651091458/assets/fonts/HYYakuHei-HEW85.woff2") format("woff2");
}
</style>

<style scoped lang="scss">
.main {
  position: relative;
  width: 100%;
  background-repeat: repeat-y;
  background-size: 100%;
  min-height: 100vh;

  .mainStatueDiv {
    background-repeat: no-repeat;
    position: relative;
    width: 100%;
    background-size: 100%;
  }
  .mainStatueDiv0 {
    padding-bottom: 0.2rem;
  }
  .kv {
    position: relative;
    img {
      width: 100%;
      height: auto;
    }
  }
  .sendPointsDiv {
    position: absolute;
    top: 3.14rem;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 0.54rem;
    // font-weight: bold;
    font-style: italic;
    font-family: "HYYakuHei-GEW";
    width: 94%;
    text-align: center;
  }
  .goToShopDiv {
    position: absolute;
    top: 4.56rem;
    left: 50%;
    transform: translateX(-50%);
    // background-color: red;
    width: 4.96rem;
    height: 0.9rem;
  }
  .getDrawDiv {
    position: absolute;
    top: 4.56rem;
    left: 50%;
    transform: translateX(-50%);
    // background-color: red;
    width: 4.96rem;
    height: 0.9rem;
  }
  .bottomDiv {
    position: relative;
    // top: 4.6rem;
    .skuDiv {
      background-repeat: no-repeat;
      background-size: 100%;
      width: 7.5rem;
      // height: 10.63rem;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      padding-top: 1.26rem;
      justify-content: space-between;
      .skuItemDiv {
        width: 48%;
        // background: red;
        // height: 4.46rem;
        position: relative;
        .buyBtnDiv {
          width: 2.64rem;
          height: 0.53rem;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    .goToShopDiv0 {
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.05rem;
      height: 0.78rem;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 0.12rem;
    }
  }
}
</style>
