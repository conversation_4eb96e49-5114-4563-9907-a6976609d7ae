interface CompressImageOptions {
  quality?: number; // 图片质量 (0-1)，默认0.8
  maxWidth?: number; // 最大宽度
  maxHeight?: number; // 最大高度
  outputFormat?: 'image/jpeg' | 'image/png' | 'image/webp'; // 输出格式
  keepRatio?: boolean; // 是否保持宽高比，默认true
  maxSize?: number; // 新增最大文件大小（单位：字节）
  maxAttempts?: number; // 最大尝试次数
}
// 加载图片的辅助函数
async function loadImage(input: File | Blob | string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      if (typeof input !== 'string') {
        const url = URL.createObjectURL(input);
        URL.revokeObjectURL(url);
      }
      resolve(img);
    };
    img.onerror = (e) => {
      if (typeof input !== 'string') {
        const url = URL.createObjectURL(input);
        URL.revokeObjectURL(url);
      }
      reject(new Error('Failed to load image'));
    };

    if (typeof input === 'string') {
      img.src = input;
    } else {
      const url = URL.createObjectURL(input);
      img.src = url;
    }
  });
}

// 计算新尺寸的辅助函数
function calculateNewSize(originalWidth: number, originalHeight: number, maxWidth: number, maxHeight: number, keepRatio: boolean): { width: number; height: number } {
  if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
    return { width: originalWidth, height: originalHeight };
  }

  const ratio = originalWidth / originalHeight;

  let newWidth = maxWidth;
  let newHeight = maxHeight;

  if (keepRatio) {
    if (maxWidth / maxHeight > ratio) {
      newWidth = maxHeight * ratio;
    } else {
      newHeight = maxWidth / ratio;
    }
  }

  return {
    width: Math.floor(newWidth),
    height: Math.floor(newHeight),
  };
}

// 生成输出文件名
function getOutputFileName(originalFile: File | Blob | string, format: string): string {
  const ext = format.split('/')[1];
  const originalName = originalFile instanceof File ? originalFile.name : 'image';
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  return `${nameWithoutExt}.${ext}`;
}
export const compressImage = async (
  file: File | Blob | string, // 支持File、Blob对象或图片URL
  options: CompressImageOptions = {},
): Promise<File> => {
  // 参数默认值处理
  const { maxAttempts = 5, quality = 0.8, maxWidth = Infinity, maxHeight = Infinity, outputFormat = 'image/jpeg', keepRatio = true } = options;
  // 创建Image对象
  const img = await loadImage(file);
  // 判断file的类型是File或Blob 小于1M直接返回源文件
  if ((file instanceof File || file instanceof Blob) && file.size <= 1 * 1024 * 1024) {
    return file instanceof File ? file : new File([file], 'original.png', { type: file instanceof File ? file.type : 'image/png', lastModified: Date.now() });
  }
  // 计算新尺寸
  const { width: newWidth, height: newHeight } = calculateNewSize(img.width, img.height, maxWidth, maxHeight, keepRatio);
  // 递归压缩函数
  const attemptCompression = async (currentQuality: number, attemptCount: number): Promise<File> => {
    // 创建Canvas
    const canvas = document.createElement('canvas');
    canvas.width = newWidth;
    canvas.height = newHeight;

    // 绘制图片
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Failed to get canvas context');
    ctx.drawImage(img, 0, 0, newWidth, newHeight);

    // 转换为Blob
    const blob = await new Promise<Blob | null>((resolve) => {
      canvas.toBlob((blob) => resolve(blob), outputFormat, currentQuality);
    });

    if (!blob) throw new Error('Image compression failed');

    // 判断blob的大小
    if (blob.size <= 1 * 1024 * 1024) {
      console.log('🚀 ~ attemptCompression ~ blob:', blob);

      return new File([blob], getOutputFileName(file, outputFormat), {
        type: blob.type,
        lastModified: Date.now(),
      });
    }

    // 终止条件
    if (attemptCount >= maxAttempts) {
      throw new Error(`无法压缩到小于1MB，最终大小：${(blob.size / 1024 / 1024).toFixed(2)}MB`);
    }

    // 指数级降低质量
    const newQuality = Math.max(currentQuality * 0.7, 0.1);
    return attemptCompression(newQuality, attemptCount + 1);
  };

  return attemptCompression(quality, 1);
};

export const compressImageToTargetSize = async (file: File | Blob | string, options: CompressImageOptions = {}): Promise<File> => {
  // 参数默认值
  const {
    maxSize = 2 * 1024 * 1024, // 默认2MB
    maxAttempts = 5, // 最大尝试次数
    quality = 0.8, // 初始质量
    ...restOptions
  } = options;

  // 检查原始文件大小
  if (file instanceof File || file instanceof Blob) {
    if (file.size <= maxSize) {
      return file instanceof File ? file : new File([file], 'original.png');
    }
  }

  // 递归压缩函数
  const attemptCompression = async (currentQuality: number, attemptCount: number): Promise<File> => {
    const compressedFile = await compressImage(file, {
      ...restOptions,
      quality: currentQuality,
    });

    // 检查压缩后大小
    if (compressedFile.size <= maxSize) {
      return compressedFile;
    }

    // 终止条件
    if (attemptCount >= maxAttempts || currentQuality <= 0.1) {
      throw new Error(`无法压缩到目标大小，最终大小：${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
    }

    // 指数级降低质量（可根据需要调整算法）
    const newQuality = Math.max(currentQuality * 0.7, 0.1);
    return attemptCompression(newQuality, attemptCount + 1);
  };

  return attemptCompression(quality, 1);
};
export default compressImage;
