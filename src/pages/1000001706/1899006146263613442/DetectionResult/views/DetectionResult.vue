<template>
    <div class="main-view">
        <div v-if="isLoadding">
            <Loading :pageNo="2" />
        </div>
        <div v-else class="result-content-box">
            <img class="logo-img" :src="require('../asset/logo.png')" alt="">
            <VanSwipe ref="swiper" :loop="false">
                <van-swipe-item>
                    <div class="result-content">
                        <div class="title" style="margin-bottom: 0.44rem;">图片扫描结果</div>
                        <div class="tips">我们使用人工智能分析了您狗狗的照片，结果显示：</div>
                        <img :src="require('../asset/tips.png')" style="width: 4.41rem;height: 0.47rem;margin: auto;margin-bottom: 0.4rem;" />
                        <div class="ai-res-box" :style="{ backgroundImage: `url(${require('../asset/aiResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}`}">
                            <div class="ai-item-box">
                                <div>已分析牙齿总数</div>
                                <div class="num-text">{{ aiRes?.teethScanned }}</div>
                            </div>
                            <div class="ai-item-box">
                                <div>有牙结石堆积<br>可见迹象的牙齿数</div>
                                <div class="num-text">{{ aiRes?.tartarTeethCount }}</div>
                            </div>
                            <div class="ai-item-box">
                                <div>有牙龈发炎<br>可见迹象的区域数量</div>
                                <div class="num-text">{{ aiRes?.gumInflammationCount }}</div>
                            </div>
                        </div>
                    </div>
                </van-swipe-item>
                <van-swipe-item>
                    <div class="result-content">
                        <div class="title">问卷调查结果</div>
                        <div class="tips">根据您在问卷中提供的信息，您的狗狗可能表现</div>
                        <div class="tips" style="margin-bottom: 0.36rem;">出一种或多种潜在口腔健康问题的迹象：</div>
                        <div class="qustion-res-item">
                            <div style="display: flex;align-items: flex-start;">
                                <div class="qustion-check-box">
                                    <img v-if="qustionRes?.ifHalitosis" style="width: 100%;" :src="require('../asset/check.png')"/>
                                </div>
                                <div style="line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${qustionRes?.ifHalitosis ? PAGE_CONFIG.mainBgColor: '#000'}`}">口臭</div>
                            </div>
                            <div class="qustion-desc">口臭是由口腔中的细菌释放出难闻的化合物所导致，这可能是口腔有健康问题的一个迹象。</div>
                        </div>
                        <div class="qustion-res-item">
                            <div style="display: flex;align-items: flex-start;">
                                <div class="qustion-check-box">
                                    <img v-if="qustionRes?.ifBleeding" style="width: 100%;" :src="require('../asset/check.png')"/>
                                </div>
                                <div style="line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${qustionRes?.ifBleeding ? PAGE_CONFIG.mainBgColor: '#000'}`}">出血</div>
                            </div>
                            <div class="qustion-desc">咀嚼或进食时出血可能提示有牙龈发炎。</div>
                        </div>
                        <div class="qustion-res-item">
                            <div style="display: flex;align-items: flex-start;">
                                <div class="qustion-check-box">
                                    <img v-if="qustionRes?.ifDiscomfort" style="width: 100%;" :src="require('../asset/check.png')"/>
                                </div>
                                <div style="line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${qustionRes?.ifDiscomfort ? PAGE_CONFIG.mainBgColor: '#000'}`}">不适</div>
                            </div>
                            <div class="qustion-desc">口腔不适可能提示有口腔健康问题。</div>
                        </div>
                        <div class="qustion-tips-box">
                            <div v-if="!qustionRes?.ifHalitosis && !qustionRes?.ifBleeding && !qustionRes?.ifDiscomfort">如果您注意到上述列出的任何问题，应该带狗狗进行全面的牙科检查。如果这些问题不存在，也需要关注狗狗口腔的日常护理，定期为它刷牙。</div>
                            <div v-else>平日注意狗狗牙齿养护是重中之重,应当注意定期刷牙，同时配合牙齿清洁用品进行清洗。</div>
                        </div>
                        <div class="qustion-bottom">
                            <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="toEnd">下一步</div>
                            <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="() => {emits('toggleComponent', 'Post');lzReportClick('clickShare');}">分享结果</div>
                        </div>
                    </div>
                </van-swipe-item>
                <template #indicator="{ active, total }">
                    <div class="page-no-box" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">
                        <div>结果{{ `${active + 1}/${total}`}}</div>
                        <div class="bottom-div">
                            <img class="direction-icon" :src="require(`../asset/direction-left.png`)" @click="swiper.prev()"/>
                            <div style="display: flex;flex-direction: row;">
                                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 0 ? 1 : 0.2}"></div>
                                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 1 ? 1 : 0.2}"></div>
                            </div>
                            <img class="direction-icon" :src="require(`../asset/direction-right.png`)" @click="swiper.next()"/>
                        </div>
                    </div>
                </template>
            </VanSwipe>
        </div>
        <Disclaimers2 :isShow="isShowDisclaimers" @closePopup="isShowDisclaimers = false"/>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import Loading from '../components/Loading.vue';
import Disclaimers2 from '../components/Disclaimers2.vue';
import { getPetBreed, getPetList, getCheckUpResult, getQaInfo, uploadToothImg, saveQaInfo, savePetInfo } from '../config/api';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const store = useStore<RootState>();
const swiper = ref();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = computed(() => store.state.petInfo);
const toothImg = computed(() => store.state.toothImg);
const checkId = computed(() => pathParams?.checkId);
const isShowDisclaimers = ref(false);

const emits = defineEmits(['toggleComponent', 'openPopup']);
const qustionRes = ref();
const aiRes = ref();

const isLoadding = ref(true);
const pageStep = ref(0);
const resNo = ref(0);
const toEnd = () => {
  lzReportClick('continueClick');
  const getHost = window.location.href.split('/custom')[0];
  window.location.href = `${getHost}/custom/1000001706/1899006146263613442/End/?checkId=${checkId.value}`;
};
const getAiRes = () => {
  getCheckUpResult({ checkId: checkId.value }).then((ressult: any) => {
    if (ressult.status && ressult.res.data.status) {
      isLoadding.value = false;
      store.commit('setAiRes', ressult.res.data);
      aiRes.value = ressult.res.data;
      emits('openPopup');
    } else if (resNo.value <= 15) {
      setTimeout(() => {
        getAiRes();
        console.log(resNo.value);
        resNo.value += 1;
      }, 10000);
    } else {
      isLoadding.value = false;
      emits('toggleComponent', 'PageError');
    }
  });
};
const init = () => {
  getQaInfo({ checkId: checkId.value }).then((res: any) => {
    console.log(res);
    qustionRes.value = res.data;
    store.commit('setQuestionRes', res.data);
  });
  getAiRes();
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  lzReportClick('resultPage');
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
