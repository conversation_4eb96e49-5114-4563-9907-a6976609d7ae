<template>
  <div class='bg' :style='{ backgroundColor: decoData.actBgColor }'>
    <div class="header">
      <img class="kv" :src="decoData.actBg" alt="">
      <img class="rule-btn" :src="decoData.ruleBtn"  alt="">
      <img class="record-btn" :src="decoData.recordBtn"  alt="">
    </div>
    <div class="swiper-container">
     <img class="swiper-title" :src="decoData.bannerBg" alt="">
     <div class="swiper-wrapper">
        <div class="swiper-slide prize-item" v-for="(item, index) in decoData.iconList" :key="index">
          <img :src="item.icon" alt="" class="prize-img" />
        </div>
      </div>
    </div>
    <div class="gifts">
      <img class="gift-title" :src="decoData.title" alt="">
      <div class="gift-list">
        <div class="gift-item"  v-for="(item, index) in prizeList">
          <img class="gift-bg" v-if="item.moduleDesign" :src="item.moduleDesign" alt="">
          <img  class="gitt-btn" v-if="item.moduleDesign" :src="decoData.receiveBtn" alt="">
        </div>
      </div>
    </div>
  </div>

</template>

<script setup lang='ts'>
import { inject, nextTick, onMounted, ref } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import useSendMessage from '@/hooks/useSendMessage';
import usePostMessage from '@/hooks/usePostMessage';

const activityData = inject('activityData') as any;
const decoData = ref<any>(inject('decoData') as any);
Swiper.use([Autoplay]);

const prizeList = ref<any>([]);



const { registerHandler } = usePostMessage();

let prizeSwiper: Swiper;
const initSwiper = () => {
  nextTick(() => {
    if (prizeSwiper) {
      prizeSwiper.destroy();
    }
    
    // 只有当 iconList 超过4个时才启用自动滚动和循环
    const shouldAutoplay = decoData.value.iconList && decoData.value.iconList.length > 4;
    
    prizeSwiper = new Swiper('.swiper-container', {
      autoplay: shouldAutoplay ? {
        delay: 1000,
        stopOnLastSlide: false,
        disableOnInteraction: false,
      } : false,
      loop: shouldAutoplay,
      slidesPerView: 4,
      loopedSlides: shouldAutoplay ? 8 : 0,
      spaceBetween: 10,
    });
  });
};

// 装修数据监听
registerHandler('deco', (data: any) => {
  decoData.value = data;
  initSwiper();
});
// 活动数据监听
registerHandler('activity', (data: any) => {
  console.log(data, 'activity')
  const { activity90203PrizeModuleList } = data;
  const result = activity90203PrizeModuleList.map((e: any) => {
    return {
      moduleDesign: e.moduleDesign
    }
  })
  prizeList.value = result;
});




onMounted(async () => {
  useSendMessage('mounted', 'sendMounted', true);
  if (decoData.value) {
    initSwiper();
  }
  if (activityData) {
    prizeList.value = activityData.activity90203PrizeModuleList.map((e: any) => {
      return {
        moduleDesign: e.moduleDesign
      }
    })
  }
});




</script>

<style scoped lang='scss'>


.bg {
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
}

.header {
  position: relative;
  .kv {
    width: 100%;
    display: block;
    object-fit: contain;
  }
  .rule-btn {
    position: absolute;
    top: 2.4rem;
    right: 0;
    width: 3rem;
    display: block;
    object-fit: contain;
    width: 1.1rem;
  }
  .record-btn {
    position: absolute;
    top: 3.05rem;
    right: 0;
    width: 100%;
    display: block;
    object-fit: contain;
    width: 1.1rem;
  }
}
.swiper-container {
  box-sizing: border-box;
  width: 100%;
  padding: 0 0.2rem;

  .swiper-title {
    width: 100%;
    display: block;
    object-fit: contain;
  }
  
  .swiper-wrapper {
    margin-top: .15rem;
  }
  
  .swiper-slide {
    width: 1.65rem;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .prize-img {
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }
}
.gifts {
  width: 100%;
  box-sizing: border-box;
  padding: 0 0.2rem 1rem;

  .gift-title {  margin: 0 auto;
    width: 6rem;
    display: block;
    object-fit: contain;
  }
  .gift-list {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;

    .gift-item {
      position: relative;
      .gift-bg {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .gitt-btn {
        position: absolute;
        top: .85rem;
        right: .48rem;
        width: 1.2rem;
        object-fit: contain;
        
        &.gitt-btn-disabled {
          filter: grayscale(100%);
          opacity: 0.5;
          pointer-events: none;
        }
      }
    }
  }
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
