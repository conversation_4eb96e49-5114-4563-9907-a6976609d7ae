/*
 * @Description: 林某
 */
import '@/style/reset.scss';
import { createApp } from 'vue';
import { init } from '@/utils';
import './style/index.scss';
import { initRem } from '@/utils/client';
import '@/style';
import root from './App.vue';
import { InitRequest } from '@/types/InitRequest';

initRem(750);

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  showFinishedPage: true,
  showUnStartPage: true,
  // disableShare: true,
  shopId: '1000002984',
  activityMainId: '1919636390914605057',
};

// 初始化页面,
init(config).then(({ baseInfo, pathParams }) => {
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.mount('#app');
});
