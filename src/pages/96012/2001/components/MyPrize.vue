<template>
  <div class="rule-bk">
    <div class="close" @click="close" />
    <div class="prizeTitle">
      <div class="name">奖品名称</div>
      <div class="status">操作</div>
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="prizeCode">
          <div>{{ item.prizeName }}</div>
          <div class="prizeCodeDate">
            <div class="date">{{ formatDate(item.prizeCodeDate || '') }}</div>
            <div class="time">{{ formatTime(item.prizeCodeDate || '') }}</div>
          </div>
        </div>
        <div class="status" v-if="item.prizeType === 3">
          <div class="blackBtn" v-if="item.writeAddress" @click="changAddress(item)">已填写</div>
          <div class="blackBtn" v-else @click="changAddress(item)">填写地址</div>
        </div>
        <div class="status" v-else-if="item.prizeType === 7">
          <div class="blackBtn copy-btn" :copy-text="getCopyText(item.prizeContent)">复制卡密</div>
        </div>
        <div class="status" v-else>
          <div class="green" v-if="item.prizeName !== '未开奖' && item.prizeName !== '未中奖' && item.prizeName !== '发奖失败'">已发放</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无数据~</div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="center">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import Clipboard from 'clipboard';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  activityPrizeId?: string;
  address?: string;
  addressId?: string;
  city?: string;
  county?: string;
  createTime?: string;
  deliverName?: string;
  deliverNo?: string;
  deliveryStatus?: number;
  isFuLuWaitingReceive?: boolean;
  mobile?: string;
  prizeCode?: number;
  prizeCodeDate?: string;
  prizeContent?: string;
  prizeImg?: string;
  prizeName?: string;
  prizeType: number;
  province?: string;
  realName?: string;
  recordId?: string;
  userPrizeId?: string;
  writeAddress?: boolean;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/96012/myRankPrize');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

/**
 * 复制礼品卡信息
 * @param content
 */
const getCopyText = (content:any) => {
  let text = '';
  const prizeContent = JSON.parse(content);
  if (prizeContent.cardNumber && prizeContent.cardPassword) {
    text = `卡号：${prizeContent.cardNumber}\n卡密：${prizeContent.cardPassword}`;
  } else if (prizeContent.cardNumber && !prizeContent.cardPassword) {
    text = `卡号：${prizeContent.cardNumber}`;
  }
  return text;
};

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  writeAddress: false,
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束');
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.recordId;
  Object.keys(echoData).forEach((key) => {
    echoData[key as keyof FormType] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 格式化日期 YYYY-MM-DD
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return dayjs(dateString).format('YYYY-MM-DD');
};

// 格式化时间 hh:mm:ss
const formatTime = (dateString: string) => {
  if (!dateString) return '';
  return dayjs(dateString).format('HH:mm:ss');
};

</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/338662/11/25514/13345/6909e0a9Fc7298935/75832fabb462ac65.png);
  background-size: 100% 100%;
  width: 6.5rem;
  height: 10.53rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 2rem;

  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.79rem;
    height: 0.79rem;
  }
  .prizeTitle {
    padding: 0 0.4rem 0.2rem;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.24rem;
    .time,
    .status {
      width: 30%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 0.4rem;
    }
    .name {
      width: 32%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 0.4rem;
    }
  }
  .content {
    height: 7.5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    padding: 0 0.4rem;
    .prize {
      padding: 0 0 0.2rem;
      color: #000;
      display: flex;
      /* align-items: center; */
      justify-content: space-between;
      .prizeCode {
        width: 30%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 0.4rem;
        font-weight: bold;
        .prizeCodeDate{
          font-size: 0.18rem;
          color: #999999;
          text-align: left;
          font-weight: normal;
          width: 1rem;
          margin: 0 auto;
          line-height: 0.3rem;
        }
      }
      .name {
        width: 32%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 0.4rem;
      }
      .grayName{
        color: #666;
      }
      .status {
        width: 30%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 0.4rem;
      }
      .blackBtn{
        width: 1.28rem;
        height: 0.5rem;
        background-color: #000000;
        color: #ffffff;
        border-radius: 0.25rem;
        margin: 0 auto;
        font-size: 0.24rem;
        line-height: 0.5rem;
      }
    }

    .no-data {
      text-align: center;
      line-height: 5.2rem;
      font-size: 0.24rem;
      color: #666666;
    }
  }
}
</style>
