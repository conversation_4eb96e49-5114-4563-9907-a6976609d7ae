<template>
  <div class='bg' :style='{backgroundImage:`url(${furnish.pageBg})`,backgroundColor:furnish.actBgColor}'>
    <div class='header-btn-box'>
      <div class='header-btn' v-for='(btn, index) in btnList' :key='index' @click='btn.event'></div>
    </div>

    <div class='sign-view'>
      <img src='//img10.360buyimg.com/imgzone/jfs/t1/298986/13/20674/17196/686736d4F066e9a93/f60fc1a19a08d20f.png' style='width: 2.6rem' class='sign-btn' alt='' @click='clickShowTosat()'>
    </div>

    <div class='act-strategy'>
      <img :src='furnish.actStrategy' alt=''>
    </div>

    <div class='repurchase-gift-view' v-if='activityInfo.prizeList?.length>0'>
      <img :src='activityInfo?.prizeList[0]?.prizeImg' class='gift-image' alt=''>
      <div class='gift-info-view'>
        <div class='gift-name'><i>{{ activityInfo?.prizeList[0]?.prizeName }}</i></div>
        <div class='gift-stock'><i>剩余库存：{{ activityInfo?.prizeList[0]?.remainingCount ?? 0 }}</i></div>
        <img src='//img10.360buyimg.com/imgzone/jfs/t1/301237/11/8632/7365/686736d4F58adb88a/35fd989a1f1baa58.png' @click='clickShowTosat()' class='gift-btn' alt=''>
      </div>
    </div>

    <div class='sku-module' v-if='activityInfo.exposureSkuList?.length>0'>
      <div class='sku-list' :style="{height:activityInfo.exposureSkuList?.length>=4?'12rem':'8.7rem'}">
        <div class='sku-item' v-for='(item,index) in activityInfo.exposureSkuList' :key='index'>
          <div class='sku-image'>
            <img :src='item?.skuMainPicture' style='width: 2rem;max-height: 2rem' alt=''>
          </div>
          <div class='two-line-omit sku-name'><i>{{ item?.skuName }}</i></div>
          <img @click='clickShowTosat()' style='width: 1.72rem' src='//img10.360buyimg.com/imgzone/jfs/t1/299364/12/20690/8741/686736d4F8ef5866f/24080ba45bc078ab.png' alt=''>
        </div>
      </div>
    </div>

    <VanPopup teleport='body' v-model:show='showRule' :close-on-click-overlay='false'>
      <div class='rule-box'>
        <div class='content' v-html='activityInfo.rules' v-if='activityInfo.rules'></div>
        <div class='content no-data'>暂无活动规则</div>
        <div class='close' @click='showRule=false'></div>
      </div>
    </VanPopup>

    <VanPopup teleport='body' v-model:show='showMyPrize' :close-on-click-overlay='false'>
      <div class='myPrize-box myPrize-none-data'>
        <div class='close' @click='showMyPrize=false'></div>
      </div>
    </VanPopup>
  </div>
</template>

<script setup lang='ts'>
import { ref, onMounted, onUnmounted, inject, nextTick } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import '../style/index.scss';
import { showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper-bundle.css'; // 引入Swiper样式

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showOrder = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const mySwiper = ref<Swiper>();
const bannerSwiper = ref();
// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
Swiper.use([Autoplay]); // 使用Swiper的扩展模块

const clickShowTosat = () => {
  showToast('活动预览，仅供查看');
};

const activityInfo = ref({});
const currentTab = ref(0);
const currentSerise = ref({}); // 当前选择的阶梯系列
const setDataInfo = (data: any) => { // 数据赋值
  activityInfo.value = data;
  if (data.rules) {
    ruleTest.value = data.rules;
  }
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '',
    event: () => {
      showMyPrize.value = true;
    },
  },
];
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(res.data.data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', () => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang='scss'>
img[src=""], img:not([src]) {
  opacity: 0;
}

.rule-box {
  width: 6.15rem;
  height: 8.5rem;
  position: relative;
  padding-top: 1rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/308239/32/15014/28203/686b3275F911bf44f/e609d3d1237765fa.png");
    repeat: no-repeat;
    size: contain;
  };

  .content {
    height: 8.7rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.2rem;
    font-weight: bold;
    color: #ce1a15;
    padding: 0.2rem .4rem;
    white-space: pre-wrap;
  }
}

.myPrize-none-data {
  height: 8.53rem !important;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/310205/22/15194/76765/686b3274F4904367a/cb521fb13afb0d28.png") !important;
}

.myPrize-box {
  width: 6.31rem;
  height: 9.53rem;
  position: relative;
  padding-top: 4.6rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/310797/40/15009/79311/686b3273F95290edc/368e09c08e3b08ad.png");
    repeat: no-repeat;
    size: contain;
  };

  .content {
    width: 6.1rem;
    height: 2.4rem;
    border: 0.1rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize-view {
      height: 4rem;
      overflow-y: auto;

      ul {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: .15rem;
        line-height: .5rem;
        border-bottom: 1px solid #e7d4c0;
        padding-bottom: .15rem;

        li {
          font-size: .22rem;
          color: #4f4f4f;
          text-align: center;
          width: 33%;
          line-height: .35rem;
        }
      }
    }
  }
}


.no-data {
  text-align: center;
  line-height: 4.5rem;
  font-size: 0.28rem;
  color: #8c8c8c !important;
}

.close {
  width: .8rem;
  height: .8rem;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
