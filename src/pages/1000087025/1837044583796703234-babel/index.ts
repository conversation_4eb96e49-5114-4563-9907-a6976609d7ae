import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem();
const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  activityMainId: '1837044583796703234',
  shopId: '1000087025',
};

document.addEventListener('DOMContentLoaded', () => {
  init(config).then(async ({ baseInfo, pathParams }) => {
    // 设置页面title
    document.title = baseInfo.activityName;
    app.provide('baseInfo', baseInfo);
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, {});
    app.mount('#app');
  });
});
