import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  urlPattern: '/custom/:activityType/:templateCode',
  disableNotice: true,
};

const _decoData = {
  kv: '//img10.360buyimg.com/imgzone/jfs/t1/301146/25/1041/450102/6811c63bFb2424289/cf05c47d54e71e0a.png',
  pageBg: '#d9bf99',
  gitft1: '//img10.360buyimg.com/imgzone/jfs/t1/298416/33/3767/267860/6819f3d8F8deb8241/5c63b1c6e2393d3b.png',
  gitft2: '//img10.360buyimg.com/imgzone/jfs/t1/280435/33/28128/285982/6819f3d9Fbf6ec9e1/3fe83fae60bb9eab.png',
  step: '//img10.360buyimg.com/imgzone/jfs/t1/291704/32/3318/255020/6819f828Ff3123361/eea0ffe96687c44a.png',
  lockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/172373/31/46975/20071/665482e7F6a5d9eaa/de0be732c54f12ef.png',
  hadLockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/209580/23/42412/14162/665482f0Fb93bd43a/37e816c4c4c38a03.png',
  lockFalseBtn: '//img10.360buyimg.com/imgzone/jfs/t1/193910/10/46280/14780/66546f57Febb8f4ca/4bb2d66df814ca2c.png',
  successBk: '//img10.360buyimg.com/imgzone/jfs/t1/171777/30/46572/23666/665e82b1Fa303832a/b6386eee122fb6c0.png',
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/204029/35/42959/26694/6655a9aaF08efa147/48ee85ea92f9064d.png',
  goShopBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/298439/10/1653/9103/6811c63cF5261cfd6/604d0d553ad5fb44.png',
  jumpUrl: '',
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo.activityName;
  if (!baseInfo.supportLevels) {
    window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
  }
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('decoData', decoData);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
