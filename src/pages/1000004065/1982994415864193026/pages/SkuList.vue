<!--
* @Description: caoshijie
* @Date: 2025-10-31 10:38:11
* @Description: 曝光商品
* @FilePath: src\pages\1000004065\1982994415864193026\components\SkuList.vue 
-->
<template>
  <div class="sku-list">
    <div class="sku">
      <img class="sku-img" @click="gotoSkuPage(item.skuId)" v-for="item in skuList" :src="item.skuImg" alt="" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { gotoSkuPage } from '@/utils/platforms/jump';
import { inject } from 'vue';

const decoData = inject('decoData') as any;
const { skuList } = decoData;
</script>
<style lang="scss" scoped>
.sku-list {
  margin-top: 0.2rem;
  width: 7.02rem;
  height: 11.07rem;
  margin: 0 auto;
  background: url(../assets/sku/sku-bg.png) no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 1.2rem 0.3rem;
  .sku {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.1rem;
    overflow-y: auto;
    max-height: 9.5rem;
    border-radius: 0.2rem;
    img {
      width: 100%;
    }
  }
}
</style>
