// 美素佳儿 新客认证
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import App from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
// 页面自适应
initRem(750);

const app = createApp(App);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableShare: true,
  disableDecorate: true,
  disableThreshold: true,
};

init(config).then(({ baseInfo, pathParams, userInfo }) => {
  console.log('🚀 ~ pathParams:', pathParams);
  // 设置页面title
  document.title = baseInfo?.activityName || '未命名';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('baseUserInfo', userInfo);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
