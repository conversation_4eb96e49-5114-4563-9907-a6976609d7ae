<template>
  <div class="bg">
    <div class="mineInfoBox" :style="furnishStyles.mineBg.value">
      <img class="mineAvatar" :src="mineInfo.avatar" alt="">
      <div class="nickName">{{mineInfo.nickName}}</div>
      <div class="progressLineBox">
        <div>我的人气值：</div>
        <div class="progress">
          <div class="bubble" :style="{ width: progressWidth }"/>
        </div>
        <div class="rate">{{mineInfo.fansTotal}}</div>
      </div>
    </div>
    <div class="mainTabArea">
      <div class="mainTabWrapper" v-for="(tab, index) in tabList" :key="index">
        <div class="mainTab" :style="index === activeMineTab ? 'color: #000' : 'color:#666'" @click="switchMineTab(index)">{{tab.title}}</div>
        <!-- 选中指示器 -->
        <div :class="index === activeMineTab ? 'activeTabIndicator' : 'tabIndicator'"/>
      </div>
    </div>
    <div v-if="activeMineTab === 0" class="mineMainBox">
      <div v-if="minePublish.length" class="showImgArea" >
        <ImagesList
          :imgList="minePublish"
          :showUserInfo="true"
          :showRank="false"
          :showChangeShare="true"
          :hasMoreData="hasMoreDataPublish"
          :isLoadingMore="isLoadingMorePublish"
          @changeMyImages="changeMyImages"
          @loadMore="handleLoadMorePublish"
        />
      </div>
      <div v-else class="noData">
        <div class="noDataTitle">你还未发布内容哦~</div>
        <div class="noDataBtn" @click="toUploadPage">点击发布</div>
      </div>
    </div>
    <div v-else class="mineMainBox">
      <div v-if="mineLiked.length" class="showImgArea">
        <ImagesList
          :imgList="mineLiked"
          :showUserInfo="true"
          :showRank="false"
          :showChangeShare="false"
          :hasMoreData="hasMoreDataLiked"
          :isLoadingMore="isLoadingMoreLiked"
          @loadMore="handleLoadMoreLiked"
        />
      </div>
      <div v-else class="noData">
        <div class="noDataTitle">你还未点赞内容哦~</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';
import {ref, onMounted, nextTick, computed, defineProps, inject, defineEmits} from 'vue';
import ImagesList from "../components/ImagesList.vue";
import {closeToast, showLoadingToast} from "vant";
import {checkActTime} from "../ts/logic";
import { httpRequest } from "@/utils/service";
import { BaseInfo } from "@/types/BaseInfo";

const baseInfo = inject("baseInfo") as BaseInfo;

const emits = defineEmits(['toUploadPage', 'changeMyImages']);

const toUploadPage = () => {
  emits('toUploadPage');
};

const tabList = ref([
  {
    title: "我发布的",
  },
  {
    title: "我点赞的",
  },
]);

// 添加tab选中状态
const activeMineTab = ref(0);
const mineLiked = ref([
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
  //   ],
  //   title: '标题1',
  //   content: '描述1',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户1',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: true,
  //   rank: 1,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题2',
  //   content: '描述2',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户2',
  //   fans: 10009,
  //   createTime: '2025-11-20',
  //   liked: true,
  //   rank: 2,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/291461/39/27351/11615/69046e4cF2e58da62/e0ba47c13aa9d89d.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题3',
  //   content: '描述3',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户3',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: true,
  //   rank: 3,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题4',
  //   content: '描述4',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户4',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: true,
  //   rank: 4,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题5',
  //   content: '描述5',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户5',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: true,
  //   rank: 5,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题6',
  //   content: '描述6',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户6',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: true,
  //   rank: 6,
  // },
]);
const minePublish = ref([
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
  //   ],
  //   title: '标题1',
  //   content: '描述1',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户XXX',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: false,
  //   rank: 1,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题2',
  //   content: '描述2',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户XXX',
  //   fans: 10009,
  //   createTime: '2025-11-20',
  //   liked: false,
  //   rank: 2,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/291461/39/27351/11615/69046e4cF2e58da62/e0ba47c13aa9d89d.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题3',
  //   content: '描述3',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户XXX',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: false,
  //   rank: 3,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题4',
  //   content: '描述4',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户XXX',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: false,
  //   rank: 4,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题5',
  //   content: '描述5',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户XXX',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: false,
  //   rank: 5,
  // },
  // {
  //   imgList: [
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
  //     'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
  //   ],
  //   title: '标题6',
  //   content: '描述6',
  //   avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
  //   nickName: '用户XXX',
  //   fans: 1000,
  //   createTime: '2025-11-20',
  //   liked: false,
  //   rank: 6,
  // },
]);

// 分页相关状态 - 我发布的
const currentPagePublish = ref(1);
const pageSizePublish = ref(20);
const hasMoreDataPublish = ref(true);
const isLoadingMorePublish = ref(false);

// 分页相关状态 - 我点赞的
const currentPageLiked = ref(1);
const pageSizeLiked = ref(20);
const hasMoreDataLiked = ref(true);
const isLoadingMoreLiked = ref(false);

const queryWay = ref(1);

// 重置分页状态
const resetPagination = (type: 'publish' | 'liked') => {
  if (type === 'publish') {
    currentPagePublish.value = 1;
    hasMoreDataPublish.value = true;
    isLoadingMorePublish.value = false;
  } else {
    currentPageLiked.value = 1;
    hasMoreDataLiked.value = true;
    isLoadingMoreLiked.value = false;
  }
};

// 根据分类详情获取用户动态信息
const getDynamicBySection = async (isLoadMore = false) => {
  try {
    const isPublishTab = activeMineTab.value === 0;
    const currentPage = isPublishTab ? currentPagePublish.value : currentPageLiked.value;
    const pageSize = isPublishTab ? pageSizePublish.value : pageSizeLiked.value;

    // 如果是加载更多，设置加载状态
    if (isLoadMore) {
      if (isPublishTab) {
        isLoadingMorePublish.value = true;
      } else {
        isLoadingMoreLiked.value = true;
      }
    }

    const { data } = await httpRequest.post("/96012/getDynamicBySection", {
      pageNum: currentPage,
      pageSize: pageSize,
      queryWay: queryWay.value, //查询方式 0-所有动态 1-我的动态 2-我点赞的动态
    });

    console.log(data.records);

    if (isPublishTab) {
      if (isLoadMore) {
        // 加载更多时追加数据
        minePublish.value = [...minePublish.value, ...data.records];
      } else {
        // 首次加载或切换tab时替换数据
        minePublish.value = data.records;
      }
      // 检查是否还有更多数据
      hasMoreDataPublish.value = data.records.length === pageSizePublish.value;
    } else {
      if (isLoadMore) {
        // 加载更多时追加数据
        mineLiked.value = [...mineLiked.value, ...data.records];
      } else {
        // 首次加载或切换tab时替换数据
        mineLiked.value = data.records;
      }
      // 检查是否还有更多数据
      hasMoreDataLiked.value = data.records.length === pageSizeLiked.value;
    }

  } catch (error) {
    console.error(error);
    console.log("获取动态信息失败");
  } finally {
    if (isLoadMore) {
      if (activeMineTab.value === 0) {
        isLoadingMorePublish.value = false;
      } else {
        isLoadingMoreLiked.value = false;
      }
    }
  }
};
// 主tab切换
const switchMineTab = (index: number) => {
  activeMineTab.value = index;
  queryWay.value = index + 1;
  // 重置对应tab的分页状态
  resetPagination(index === 0 ? 'publish' : 'liked');
  getDynamicBySection();
};

// 处理加载更多 - 我发布的
const handleLoadMorePublish = () => {
  if (hasMoreDataPublish.value && !isLoadingMorePublish.value) {
    currentPagePublish.value++;
    getDynamicBySection(true);
  }
};

// 处理加载更多 - 我点赞的
const handleLoadMoreLiked = () => {
  if (hasMoreDataLiked.value && !isLoadingMoreLiked.value) {
    currentPageLiked.value++;
    getDynamicBySection(true);
  }
};

const mineInfo = ref({});
// 进度条
const progressBar = ref(0);
const progressWidth = ref('');

// 个人中心主接口
const userCenterInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96012/userCenterInfo');
    mineInfo.value = data;
    progressBar.value = mineInfo.value.fansTotal / 100000;
    progressWidth.value = `${progressBar.value * 100}%`;
  } catch (e){
    console.log(e.message)
  }
}

const changeMyImages = (item) => {
  console.log('myPage修改该条', item);
  emits('changeMyImages', item);
}

const isLoadingFinish = ref(false);
const initMinePage = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([userCenterInfo(), getDynamicBySection()]);
    isLoadingFinish.value = true;
    closeToast();
    if (!checkActTime(baseInfo)) {
      return;
    }
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
initMinePage();
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding: 0 0 0.3rem 0;
  .mineInfoBox{
    width: 7.5rem;
    height: 5.19rem;
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 1.12rem 0.5rem 0;
    .mineAvatar{
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      margin: 0 auto;
    }
    .nickName{
      font-size: 0.36rem;
      color: #000000;
      margin: 0.34rem auto 0;
      text-align: center;
    }
    .progressLineBox {
      display: flex;
      margin: 0 auto;
      width: 6rem;
      position: relative;
      top: 0.6rem;
      justify-content: center;
      font-size: 0.24rem;
      color: #666666;
      .progress {
        background: url(https://img10.360buyimg.com/imgzone/jfs/t1/349283/21/20840/344/69085083Ff1bca5cf/9edd8ffa50d547db.png) no-repeat;
        background-size: 100%;
        width: 3rem;
        height: 0.15rem;
        position: relative;
        border-radius: 0.08rem;
        overflow: hidden;
        transform: translateY(25%);
        .bubble {
          margin-top: 0.01rem;
          height: 0.15rem;
          position: relative;
          border-radius: 0.08rem;
          background-color: #415fff;
        }
      }
      .rate {
        text-align: right;
        font-size: 0.24rem;
        margin: 0 0 0 0.1rem;
        color: #415fff;
      }
    }
  }
  .mainTabArea{
    width: 6.9rem;
    height: 0.6rem;
    margin: 0.3rem auto 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    overflow-x: scroll;
    background-color: #fff;
    border-radius: 0.24rem;
    font-size: 0.24rem;
    padding: 0.1rem 0 0 0;
    &::-webkit-scrollbar {
      display: none;
    }

    .mainTabWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;

      .mainTab {
        width: 3.45rem;
        height: 0.4rem;
        line-height: 0.4rem;
        text-align: center;
        font-weight: bold;
        cursor: pointer;
      }

      .tabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
        background-color: transparent;
      }
      .activeTabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
        background-color: #415fff;
      }
    }
  }
  .mineMainBox{
    width: 7.5rem;
    .showImgArea{
      width: 7.1rem;
      margin: 0 auto;
      padding: 0 0 0.73rem;
    }
    .noData{
      width: 7.1rem;
      margin: 0 auto;
      height: 4rem;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .noDataTitle{
        font-size: 0.24rem;
        color: #666666;
        margin: 0.5rem auto 0.3rem;
      }
      .noDataBtn {
        line-height: 0.6rem;
        text-align: center;
        margin: 0 auto;
        color: #fff;
        width: 3.08rem;
        height: 0.88rem;
        background-color: #000000;
        font-size: 0.24rem;
        line-height: 0.88rem;
        border-radius: 0.44rem;
      }
    }
  }
}
</style>
