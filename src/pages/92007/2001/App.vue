<template>
  <div class="page-bg">
    <div class="kv">
      <div class="btn-list">
        <div class="btn1" @click="rulePopup = true"></div>
        <div class="btn2" @click="myPrizePopup = true"></div>
      </div>
      <img :src="decoData.gitft1" class="gift1" v-if="hasGift1" />
      <img :src="decoData.gitft2" class="gift2" v-if="hasGift2" />
      <img :src="decoData.step" class="step" />
      <!-- 锁权按钮 -->
      <!-- 已锁权-->
      <img :src="decoData.hadLockBtn" class="lockBtn" v-if="lockStatus === 1" />
      <!-- 无法参与-->
      <img :src="decoData.lockFalseBtn" class="lockBtn" v-else-if="lockStatus === -1" />
      <!--锁权未开始-->
      <img :src="decoData.lockNoStart" class="lockBtn" v-else-if="lockStatus === 2" />
      <!--锁权已结束-->
      <img :src="decoData.lockHadEnd" class="lockBtn" v-else-if="lockStatus === 3" />
      <!-- 库存为空-->
      <img :src="decoData.lockFinishBtn" class="lockBtn" v-else-if="gift1Stock === 0 && gift2Stock === 0" />
      <!--      未锁权-->
      <img :src="decoData.lockBtn" class="lockBtn" @click="handleClock" v-else-if="lockStatus === 0" />
      <div class="content-text"></div>
      <div class="sku-bg">
        <div class="tab-box">
          <div :class="quanhu" @click="handleQuanhuClick"></div>
          <div :class="qihu" @click="handleQihuClick"></div>
        </div>
        <div class="sku-list-box" v-if="skuListType === 1">
          <div class="sku-item" v-for="(item, index) in quanhuSkuList" :key="index">
            <img :src="item.skuMainPicture" class="sku-img" />
            <div class="sku-name">{{ item.skuName }}</div>
            <img :src="decoData.goBuyBtn" class="buy-button" @click="gotoSkuPage(item.skuId)" />
          </div>
        </div>
        <div class="sku-list-box" v-if="skuListType === 2">
          <div class="sku-item" v-for="(item, index) in qihuSkuList" :key="index">
            <img :src="item.skuMainPicture" class="sku-img" />
            <div class="sku-name">{{ item.skuName }}</div>
            <img :src="decoData.goBuyBtn" class="buy-button" @click="gotoSkuPage(item.skuId)" />
          </div>
        </div>
      </div>
      <img :src="decoData.goShopBtn" class="go-shop-btn" @click="toLink(decoData.jumpUrl)" v-click-track="'bottomClick'" />
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="rulePopup">
    <RulePopup></RulePopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="thresholdShow">
    <div class="threshold-bk">
      <img :src="decoData.thresholdBg" alt="" class="threshold-img" />
      <div class="btn" @click="toOpenCard"></div>
      <div class="close-btn" @click="thresholdShow = false"></div>
    </div>
  </VanPopup>
  <!--  锁权确认弹窗-->
  <VanPopup teleport="body" v-model:show="lockConfirmShow">
    <LockConfirm @handleConfirm="toPay" :gift1Stock="gift1Stock" :gift2Stock="gift2Stock" :hasGift1="hasGift1" :hasGift2="hasGift2"></LockConfirm>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup">
    <MyPrize v-if="myPrizePopup"></MyPrize>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="successPopup">
    <div class="success-bk">
      <img :src="decoData.successBk" alt="" class="success-img" />
      <div class="content-text-box">
        <div class="see-text">您可在“锁权记录”查看奖品信息</div>
        <div class="see-content">{{ dayjs(orderStartTime).format('YYYY年MM月DD日HH时') }}至{{ dayjs(orderEndTime).format('YYYY年MM月DD日HH时') }}前店铺内购买指定商品满6罐或满12 罐， 确认收货后，奖品将在活动结束后 15个工作日内为您发放。</div>
      </div>
      <div class="btn" @click="toSaveAddress"></div>
      <div class="close-btn" @click="successPopup = false"></div>
    </div>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="addressPopup">
    <AddressPopup @close="addressPopup = false"></AddressPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage, gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import constant from '@/utils/constant';
import AddressPopup from './components/AddressPopup.vue';
import RulePopup from './components/RulePopup.vue';
import MyPrize from './components/MyPrize.vue';
import LockConfirm from './components/LockConfirm.vue';
import dayjs from 'dayjs';

const decoData = inject('decoData') as any;
const baseInfo = inject('baseInfo') as BaseInfo;

// 大贸店309
// const decoData = {
//   kv: '//img10.360buyimg.com/imgzone/jfs/t1/180423/16/45440/180755/6642f865F30b9e946/100d65410160a9b2.png',
//   tip1: '//img10.360buyimg.com/imgzone/jfs/t1/92074/22/39740/20462/6642f865F2a6e497d/a9daad874af58c31.png',
//   tip2: '//img10.360buyimg.com/imgzone/jfs/t1/227128/32/17576/33984/6642f865Feedb7ce8/4f6c43cea4abfdbc.png',
//   prize: '//img10.360buyimg.com/imgzone/jfs/t1/230845/22/17427/33656/6642f865F15193008/bf96b8c728aa131e.png',
//   skuBk: '//img10.360buyimg.com/imgzone/jfs/t1/123333/14/45703/7372/6642f865F7da86347/45895437b0076a3e.png',
//   successBk: '//img10.360buyimg.com/imgzone/jfs/t1/219460/28/41572/143272/66432429Fd5596227/9ff490f34f5d862d.png',
// };

const kvImg = computed(() => `url(${decoData.kv})`);
const skuBkImage = computed(() => `url(${decoData.skuBK})`);

const lockConfirmShow = ref(false);
const lockStatus = ref(0); // 锁权状态 0-未锁权 1-已锁权 -1-无法参与（已参与了1期）4-库存不足
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const successPopup = ref(false);
const addressPopup = ref(false);
const hasGift1 = ref(false);
const hasGift2 = ref(false);
const gift1Stock = ref(0);
const gift2Stock = ref(0);
const thresholdShow = ref(false);
const orderStartTime = ref('');
const orderEndTime = ref('');
const toSaveAddress = () => {
  successPopup.value = false;
  addressPopup.value = true;
};
const quanhuSkuList = ref<any[]>([]);
const qihuSkuList = ref<any[]>([]);

const toLink = (link: string) => {
  if (decoData.jumpUrl) {
    window.location.href = link;
  } else {
    gotoShopPage(baseInfo.shopId);
  }
};

// 1 全护  2 启护
const skuListType = ref(1);
const quanhu = ref('quanhu-act');
const qihu = ref('qihu');

// 点击全护
const handleQuanhuClick = () => {
  if (quanhu.value === 'quanhu-act') {
    return;
  }
  quanhu.value = 'quanhu-act';
  skuListType.value = 1;
  qihu.value = 'qihu';
};
// 点击启护
const handleQihuClick = () => {
  if (qihu.value === 'qihu-act') {
    return;
  }
  quanhu.value = 'quanhu';
  skuListType.value = 2;
  qihu.value = 'qihu-act';
};
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/92007/getExposureSku');
    const quanhulist = data.filter((item: any) => item.type === 1);
    const qihuList = data.filter((item: any) => item.type === 2);
    quanhuSkuList.value = quanhulist;
    qihuSkuList.value = qihuList;
  } catch (error: any) {
    console.error(error);
  }
};
const needCheckPay = ref(false);
const merchantOrderId = ref('');
const getInventory = async () => {
  try {
    const { data } = await httpRequest.post('/92007/getPrizes');
    const cans6Index = data.findIndex((item: any) => item.cans === 6);
    const cans12Index = data.findIndex((item: any) => item.cans === 12);
    if (cans6Index !== -1) {
      hasGift1.value = true;
      gift1Stock.value = data[cans6Index].stock;
    }
    if (cans12Index !== -1) {
      hasGift2.value = true;
      gift2Stock.value = data[cans12Index].stock;
    }
  } catch (error: any) {
    showToast(error.message);
  }
};
const getLockStatus = async () => {
  try {
    const data = await httpRequest.post('/92007/getUserLockStatus');
    lockStatus.value = data.data.lockStatus;
    orderStartTime.value = data.data.orderStartTime;
    orderEndTime.value = data.data.orderEndTime;
  } catch (error: any) {
    showToast(error?.message || '请求失败');
  }
};
const toPay = async (val: number) => {
  if (needCheckPay.value) {
    return;
  }
  try {
    showLoadingToast({
      message: '支付中...',
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/92007/createLockOrder', {
      activityMainId: baseInfo.activityMainId,
      shopId: baseInfo.shopId,
      token: sessionStorage.getItem(constant.LZ_JD_TOKEN),
      cans: val,
    });
    closeToast();
    merchantOrderId.value = data.merchantOrderId;
    const param = {
      orderId: data.orderId,
      paySign: data.paySign,
    };
    sessionStorage.setItem('merchantOrderId', data.merchantOrderId);
    const payUrl = `openapp.jdmobile://virtual?params={"category":"jump","des":"jdmp","appId":"2B43F9A518AE09BAE8789053047A685E","vapptype":"1","path":"pages/saas-pay/saas-pay.html","pageAlias":"","param":${JSON.stringify(param)}}`;
    needCheckPay.value = true;
    window.location.href = payUrl;
    console.log(payUrl);
  } catch (error: any) {
    showToast(error.message);
  } finally {
    lockConfirmShow.value = false;
  }
};
// 查看是否支付完成
const getPayMember = async () => {
  try {
    const res = await httpRequest.post('/92007/checkPay', {
      activityId: baseInfo.activityMainId,
      merchantOrderId: merchantOrderId.value,
    });
    return res.data;
  } catch (error: any) {
    showToast(error.message);
    return false;
  }
};

// 轮询支付状态
const pollingPayStatus = async () => {
  const type = await getPayMember();
  if (!type) {
    setTimeout(() => {
      pollingPayStatus();
    }, 3000);
  } else {
    lockStatus.value = 1;
    showToast('支付成功');
    getLockStatus();
    successPopup.value = true;
  }
};

// 检查支付状态
const checkPay = () => {
  if (document.visibilityState !== 'visible') return;
  if (!needCheckPay.value) {
    return;
  }
  needCheckPay.value = false;
  pollingPayStatus();
};
// 锁权
const handleClock = () => {
  if (baseInfo.memberLevel <= 0) {
    thresholdShow.value = true;
    return;
  }
  lockConfirmShow.value = true;
};

// 去开卡
const toOpenCard = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

onMounted(() => {
  getInventory(); // 查询库存
  getLockStatus(); // 锁权状态
  // 从上一页返回时，重新获取数据
  document.addEventListener('visibilitychange', checkPay);
  if (baseInfo.memberLevel <= 0) {
    thresholdShow.value = true;
  }
});
onUnmounted(() => {
  document.removeEventListener('visibilitychange', checkPay);
});
getSkuList(); // 推荐商品
</script>

<style scoped lang="scss">
.page-bg {
  background: #dfd2bf;
}
.kv {
  position: relative;
  background-image: v-bind(kvImg);
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  padding-top: 2rem;
  //height: auto;
  min-height: 100vh;
  .btn-list {
    position: absolute;
    top: 0.4rem;
    right: 0;
    .btn1 {
      width: 1.2rem;
      height: 0.32rem;
      margin-bottom: 0.15rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/234256/32/18067/2908/665832c6Fa6eb5758/79e4908337570dc8.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .btn2 {
      width: 1.2rem;
      height: 0.32rem;
      margin-bottom: 0.15rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238551/1/10543/3242/665832c6Fa3f4d16d/184d783969f778c9.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .gift1 {
    width: 7.3rem;
    margin: 0 0.1rem 0.2rem;
  }
  .gift2 {
    width: 7.3rem;
    margin: 0 0.1rem 0.2rem;
  }
  .step {
    width: 7.33rem;
    margin: 0 0.1rem -0.68rem;
  }
  .lockBtn {
    width: 4rem;
    margin: 0 auto;
  }
  .content-text {
    width: 7.5rem;
    height: 3.54rem;
    text-align: left;
    margin: 0rem auto;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/283064/23/16251/28756/67f36515Fca4a144e/4b426c36a3cc3dfa.png') no-repeat;
    background-size: 100%;
    margin-top: -0.03rem;
    position: relative;
  }
  .sku-bg {
    //width: 7.2rem;
    height: 12rem;
    margin: 0 auto;
    background-image: v-bind(skuBkImage);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-top: 1.5rem;
    .tab-box {
      width: 6.08rem;
      height: 0.68rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/240897/15/9717/5334/6656cc38F4cb72912/c4de7da62153af54.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: relative;
      .quanhu-act {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242148/14/7821/18934/6656cc37Ffff1d6b4/12428ad5c441e12f.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .qihu-act {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/230058/17/18549/5047/6656cc37Fb476dbcb/a91f92d3678a4daa.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .quanhu {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/192454/4/45171/7450/6656cc37Fd1c7abd8/9a15a9a0707c0fba.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 2.41rem;
        background-position: center;
        background-repeat: no-repeat;
      }
      .qihu {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/187050/36/45647/5511/6656cc37F5543637e/fc6d0df610e810fa.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 1.54rem;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
    .sku-list-box {
      width: 7rem;
      height: 8rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      overflow-y: scroll;
      margin-top: 0.3rem;
      .sku-item {
        width: 3rem;
        height: 4rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .sku-img {
          width: 3rem;
        }
        .sku-name {
          color: #6f492c;
          height: 0.5rem;
          line-height: 0.5rem;
          width: 3rem;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .buy-button {
          width: 2rem;
        }
      }
    }
  }
  .join-btn {
    width: 5rem;
    margin: 0 auto;
    margin-top: 0.3rem;
  }
  .go-shop-btn {
    width: 7.5rem;
    margin-top: 0.3rem;
  }
}
.success-bk {
  position: relative;
  .success-img {
    width: 6.5rem;
    height: auto;
  }
  .content-text-box {
    position: absolute;
    width: 5rem;
    height: 3rem;
    left: 50%;
    transform: translate(-50%);
    bottom: 2.3rem;
    font-size: 0.24rem;
    text-align: center;
    color: #8c5c35;
    .see-text {
      height: 0.8rem;
    }
    .see-content {
      line-height: 0.4rem;
    }
  }
  .btn {
    position: absolute;
    top: 6.2rem;
    left: 50%;
    transform: translate(-50%);
    width: 2rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
  .close-btn {
    position: absolute;
    bottom: 0.1rem;
    left: 50%;
    transform: translate(-50%);
    width: 0.7rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
}
.threshold-bk {
  position: relative;
  .threshold-img {
    width: 6.5rem;
    height: auto;
  }
  .btn {
    position: absolute;
    top: 6.2rem;
    left: 50%;
    transform: translate(-50%);
    width: 2rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
  .close-btn {
    position: absolute;
    bottom: 0.1rem;
    left: 50%;
    transform: translate(-50%);
    width: 0.7rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
}
</style>
<style>
@font-face {
  font-family: 'FZZZHJT';
  font-style: normal;
  font-weight: normal;
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZZZHJT/FZZZHJT.TTF');
  font-display: swap;
}
* {
  font-family: 'FZZZHJT';
}
</style>
