/*
 * @Description: 林某
 */
import '@/style/reset.scss';
import { createApp } from 'vue';
import { init } from '@/utils';
import './style/index.scss';
import { initRem } from '@/utils/client';
import '@/style';
import root from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem(750);

// 设置页面title
document.title = '';

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  // disableShare: true,
  shopId: '1000429402',
  activityMainId: '1970377377062064129',
};

// 初始化页面,
init(config).then(({ baseInfo, pathParams }) => {
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
