<template>
  <VanPopup v-model:show="isShow">
    <div class="popup-bg">
      <div class="address-info">
        <div class="address-row">
          <div class="title"><span style="color: #ff3333;">*</span>姓名：</div>
          <input type="text" placeholder="请输入姓名" v-model="form.realName" oninput="value=value.replace(/[^\d]/g,'')" maxlength="20" :disabled="isReadOnly" />
        </div>
        <div class="address-row">
          <div class="title"><span style="color: #ff3333;">*</span>电话：</div>
          <input type="text" placeholder="收货人手机号" v-model="form.mobile" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" :disabled="isReadOnly" />
        </div>
        <div class="address-row">
          <div class="title"><span style="color: #ff3333;">*</span>地区：</div>
          <input type="text" placeholder="选择省/市/区" v-model="addressCode" readonly="true" @click="addressSelects = true" :disabled="isReadOnly" />
        </div>
        <div class="address-row">
          <div class="title"><span style="color: #ff3333;">*</span>详细地址：</div>
          <input type="text" placeholder="街道门牌号" v-model="form.address" maxlength="30" :disabled="isReadOnly" />
        </div>
      </div>
      <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/343577/5/12488/11513/68ee1d3fFcc080d03/5840cf1e9bac2532.png" alt="" @click="checkForm" v-if="!isReadOnly" />
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/288245/31/9579/1772/6836db46Fb2c9f682/860212cf7162771c.png" @click="close" />
    </div>
    <!--地址选择-->
    <VanPopup v-model:show="addressSelects" teleport="#app" position="bottom">
      <VanArea title="请选择省市区" :area-list="areaList" @confirm="confirmAddress" @cancel="onCancel" />
    </VanPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { areaList } from '@vant/area-data';
import { containsEmoji, containsSpecialChars, isPhoneNumber, validateDataWithRules } from '@/utils/platforms/validator';
import { writeAddress } from '../script/ajax';
import { showToast } from 'vant';

const props = defineProps({
  showAddress: {
    type: Boolean,
    default: false,
  },
  addressInfo: {
    type: Object,
    default: () => ({}),
  },
  id: {
    type: String,
    required: true,
    default: '',
  },
});
const emit = defineEmits(['close']);
const isReadOnly = computed(() => props.addressInfo.mobile);
const form = reactive({
  activityPrizeId: '',
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '', // 区
  address: '',
});
const addressCode = ref('');
const addressSelects = ref(false);
// 确认三联地址信息
const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList?.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
  addressCode.value = addressItemList.selectedOptions.map((item: any) => item.text).join('/');
};
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
const ruleValidate = {
  mobile: [
    {
      required: true,
      message: '请输入电话号码',
    },
    {
      validator: isPhoneNumber,
      message: '请输入正确的电话号码',
    },
  ],
  province: [
    {
      required: true,
      message: '请选择省/市/区',
    },
  ],
  address: [
    {
      required: true,
      message: '请输入详细地址',
    },
    {
      validator: containsEmoji,
      message: '详细地址不能包含表情',
    },
  ],
};
const checkForm = async () => {
  const valid = validateDataWithRules(ruleValidate, form);
  if (!valid) return;
  const res = await writeAddress({ ...form, activityPrizeId: props.id });
  showToast('保存成功~');
  close();
};
const close = () => {
  Object.assign(form, {
    realName: '匿名',
    mobile: '',
    province: '',
    city: '',
    county: '', // 区
    addressCode: '',
    address: '',
  });
  emit('close');
};
const isShow = computed(() => props.showAddress);
watch(
  () => props.addressInfo,
  (newVal) => {
    if (newVal.mobile) {
      Object.assign(form, { ...props.addressInfo });
      addressCode.value = `${form.province}/${form.city}/${form.county}`;
    }
  },
  {
    deep: true,
  },
);
</script>
<style lang="scss" scoped>
.popup-bg {
  width: 5.91rem;
  height: 6.72rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/245222/26/32918/94275/68ee1d6bFcb7f6567/220d50d52a1b13da.png') no-repeat;
  background-size: 100%;
  padding-top: 1.6rem;
  margin-top: 0.5rem;
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0.1rem;
    right: 0.2rem;
  }
  .address-info {
    width: 4.52rem;
    height: 6.5rem;
    margin: 0 auto;
    .address-row {
      height: 0.71rem;
      display: flex;
      background-color: #fff;
      border-radius: 0.08rem;
      align-items: center;
      margin-bottom: 0.1rem;
      font-size: 0.2rem;
      .title {
        width: 1.25rem;
        // text-align: center;
        box-sizing: border-box;
        padding-left: 0.17rem;
        word-break: keep-all;
      }
      input {
        background: none;
        border: none;
        color: #647242;
      }
    }
  }
  .btn {
    position: absolute;
    bottom: 0.3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.72rem;
  }
}
</style>
