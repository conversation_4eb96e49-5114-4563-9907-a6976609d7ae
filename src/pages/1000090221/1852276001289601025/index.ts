import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';

initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};

const getDecoData = async () => {
  try {
    const data = {
      kv: '//img10.360buyimg.com/imgzone/jfs/t1/221653/22/46662/146027/67275473F72200d52/97d7c299aa3bc885.png',
      gitft1: '//img10.360buyimg.com/imgzone/jfs/t1/129591/31/50632/320180/67275471Fe516c267/ec6a30039a4766af.png',
      gitft2: '//img10.360buyimg.com/imgzone/jfs/t1/163877/7/51127/327276/67275471F6dc4d7b2/728366190ed423f6.png',
      step: '//img10.360buyimg.com/imgzone/jfs/t1/99326/14/54883/51393/67275470F317e58a4/a330c2ccee3d09bc.png',
      lockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/101145/5/45565/6867/6655b699F4caeaf44/f44e3dcbcce92f8c.png',
      hadLockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/209580/23/42412/14162/665482f0Fb93bd43a/37e816c4c4c38a03.png',
      lockFalseBtn: '//img10.360buyimg.com/imgzone/jfs/t1/231894/31/19337/19165/66558f31F556f7d58/eb46b9c4c0a4838e.png',
      lockFinishBtn: '//img10.360buyimg.com/imgzone/jfs/t1/193910/10/46280/14780/66546f57Febb8f4ca/4bb2d66df814ca2c.png',
      lockNoStart: '//img10.360buyimg.com/imgzone/jfs/t1/186507/24/46943/10695/665d854eF1c92b82a/94f5a3c077ac2e49.png',
      lockHadEnd: '//img10.360buyimg.com/imgzone/jfs/t1/227316/34/19792/10483/665d80beF4aaaefaf/af4c2191c1bbc78c.png',
      skuBK: '//img10.360buyimg.com/imgzone/jfs/t1/160695/36/42695/54021/6655b69aF80e8f33c/29d47d402ecd7377.png',
      goBuyBtn: '//img10.360buyimg.com/imgzone/jfs/t1/237758/25/16559/4662/6655b69aF93978327/6b8a66110fbf4ddc.png',
      goShopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/100989/34/50644/14002/66f4f60bFbb0ea0ab/8995c224a891a685.png',
      successBk: '//img10.360buyimg.com/imgzone/jfs/t1/171777/30/46572/23666/665e82b1Fa303832a/b6386eee122fb6c0.png',
      thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/204029/35/42959/26694/6655a9aaF08efa147/48ee85ea92f9064d.png',
    };
    // const { data } = await httpRequest.post('common/getActivityConfig');
    app.provide('decoData', data);
  } catch (error: any) {
    console.error(error);
  }
};

init(config).then(async ({ baseInfo, pathParams }) => {
  // 设置页面title
  document.title = baseInfo.activityName;
  if (!baseInfo.supportLevels) {
    window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
  }
  await getDecoData();
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
