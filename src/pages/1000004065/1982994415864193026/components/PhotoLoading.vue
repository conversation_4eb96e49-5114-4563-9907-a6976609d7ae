<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false">
    <div class="dialog">
      <div class="close_icon"></div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getRules } from '../script/ajax';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 6.51rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/342647/20/21224/58702/690b1dd4F3fe96ce9/a9615ab1238a7c1f.png) no-repeat;
  background-size: contain;
}
</style>
