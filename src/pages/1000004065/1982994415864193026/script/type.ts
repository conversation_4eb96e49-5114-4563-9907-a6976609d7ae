export interface ActInfo {
  /**
   * 预约新品赢好礼sku
   */
  appointmentSkuId: number;

  /**
   * 是否预约新品sku
   */
  appointmentSkuStatus: boolean;

  /**
   * 下单1元权益包sku
   */
  rightsSkuId: number;

  /**
   * 是否是今日新用户
   */
  todayNewUser: boolean;

  /**
   * 用户当前星光值
   */
  userCurrentStarValue: number;
}
export interface StageItem {
  /**
   * 是否解锁，true-已解锁；false-未解锁
   */
  lockStatus: boolean;

  /**
   * 奖品图片
   */
  prizeImg: string;

  /**
   * 奖品名称
   */
  prizeName: string;

  /**
   * 排序ID
   */
  sortId: number;

  /**
   * 星光值
   */
  starValue: number;
}

export interface StagePrizeList {
  /**
   * 解锁奖励列表
   */
  stagePrizeList: StageItem[];

  /**
   * 	总星光值
   */
  totalStarValue: number;
}

export interface PrizeInfo {
  /**
   * 奖品图片
   */
  prizeImg: string;

  /**
   * 奖品名称
   */
  prizeName: string;

  /**
   * 排序ID
   */
  sortId: number;
}

export interface TaskInfo {
  /**
   * 任务完成次数
   */
  finishNum: number;

  /**
   * 任务是否完成
   */
  isFinished: boolean;

  /**
   * 任务对应的页面url
   */
  pageLink: string;

  /**
   * 任务完成赠送的健康值
   */
  perLotteryCount: number;

  /**
   * 排序id
   */
  sortId: number;

  /**
   * 任务图片
   */
  taskImg: string;

  /**
   * 任务类型
   */
  taskType: number;
  /**
   * 任务标题
   */
  title: string;

  /**
   * 任务描述信息
   */
  info: string;
  btn: string;
  hasdBtn: string;
  /**
   * 任务总次数
   */
  totalNum: number;
}
export interface MyPrize {
  /**
   * 地址字符串
   */
  address: string;

  /**
   * 地址ID
   */
  addressId: string;

  /**
   * 所在城市
   */
  city: string;

  /**
   * 所在县区
   */
  county: string;

  /**
   * 中奖时间
   */
  createTime: string; // date-time

  /**
   * 发货状态 0:未发货 1:已发货
   */
  deliveryStatus: number; // integer(int32)

  /**
   * 手机号
   */
  mobile: string;

  /**
   * 资产发放内容
   */
  prizeContent: string;

  /**
   * 奖品名称
   */
  prizeName: string;

  /**
   * 资产类型
   */
  prizeType: number; // integer(int32)

  /**
   * 所在省份
   */
  province: string;

  /**
   * 收货人
   */
  realName: string;
}
// 任务类型，枚举：1-关注店铺；3-浏览商品；4-浏览直播间；5-浏览会场；7-加购；8-下单；13-开卡；111-搜索任务；222-预约新品任务；333-下单1元权益包；555-邀请好友；666-留言；777-与宋雨琦同框；888-体验一键破框；999-学生认证
