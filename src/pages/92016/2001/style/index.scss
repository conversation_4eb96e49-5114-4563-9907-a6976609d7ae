#app {
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/287152/16/13034/8676/686736d9Fe6074278/3450d39b0c03e48d.png");
    size: 100% 100%;
    repeat: no-repeat;
  };
}

#app .van-popup {
  background-color: transparent;
}

.gray {
  color: #8b7c7c;
  filter: grayscale(100%);
}

.bg {
  overflow-x: hidden;
  position: relative;
  background-size: 100%;
  min-height: 100vh;
  max-width: 7.5rem;
  padding-top: 7rem;
  padding-bottom: 1rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/316961/3/13095/158084/686736d3F91f481e5/7dbd93e96e53a714.jpg");
    size: contain;
    repeat: no-repeat;
  };

  .no-start-mask {
    width: 100%;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9;
  }

  .act-kv {
    width: 7.5rem;
    position: absolute;
    left: 0;
    top: 0;
  }

  .header-btn-box {
    position: absolute;
    top: .3rem;
    right: 0;

    .header-btn {
      width: 1.18rem;
      height: 0.35rem;
      margin-bottom: 0.1rem;
      padding-left: .1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background: {
        repeat: no-repeat;
        size: contain;
      };
    }

    :nth-child(1) {
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/312180/15/14324/6214/686736d5F57269c16/8f56f7704a005dbb.png");
    }

    :nth-child(2) {
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/289585/26/8205/5929/686736d7F44345757/faeaf6d02c2b8ed9.png");
    }
  }

  .sign-view {
    position: absolute;
    left: 50%;
    top: 5.6rem;
    transform: translateX(-50%);
  }

  .act-strategy {
    width: 7.5rem;
    text-align: center;
    margin-top: .2rem;

    img {
      width: 7.2rem;
      margin: 0 auto;
    }
  }

  .repurchase-gift-view {
    width: 7.24rem;
    height: 3.66rem;
    position: relative;
    margin: .2rem auto 0;
    padding: .9rem 0.2rem 0.2rem .7rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/306325/24/15317/34530/686736d7F09baa63f/7d2692b463fcbb4f.png");
      repeat: no-repeat;
      size: contain;
    };

    .gift-image {
      width: 2rem;
    }

    .gift-info-view {
      width: 4rem;
      height: 2.5rem;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;

      .gift-name {
        width: 2.5rem;
        font-size: .3rem;
        color: #ca2521;
        font-weight: bold;
      }

      .gift-stock {
        width: 3rem;
        font-size: .23rem;
        color: #663e0a;
      }

      .gift-btn {
        width: 1.5rem;
      }
    }
  }

  .sku-module {
    width: 7.24rem;
    height: 13.87rem;
    position: relative;
    margin: .2rem auto 0;
    padding: 1rem .2rem .2rem;
    overflow: hidden; /* 关键：隐藏超出部分 */
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/314992/36/14326/39882/686736d8F26924999/98954ae2ac4f2b88.png");
      repeat: no-repeat;
      size: contain;
    };

    .sku-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      overflow-y: auto;
      padding: 0 .27rem;

      .sku-item {
        width: 3rem;
        margin-top: .2rem;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;

        .sku-image {
          width: 2.87rem;
          height: 2.64rem;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          background: {
            image: url("//img10.360buyimg.com/imgzone/jfs/t1/292846/19/19078/4028/686736d5Faf6bac80/1391c2c6e37bd294.png");
            repeat: no-repeat;
            size: contain;
          };
        }

        .sku-name {
          font-size: .22rem;
          color: #673d0a;
          text-align: center;
          font-weight: bold;
          padding: 0 .2rem;
          margin: .05rem 0;
        }
      }
    }

    .loading {
      width: 100%;
      text-align: center;
      padding: .15rem 0;
      color: #999;
      font-size: .2rem;
    }

    .no-more {
      width: 100%;
      text-align: center;
      padding: .15rem 0;
      color: #999;
      font-size: .2rem;
    }

  }

  .banner-module {
    .banner-image {
      width: 7.14rem;
      height: 2.8rem;
      margin: .3rem auto;
    }
  }
}

.box {
  .close {
    width: .8rem;
    height: .8rem;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}


/*超过一行显示省略号*/
.one-line-omit {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

// 超过两行显示省略号
.two-line-omit {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.gray {
  filter: grayscale(1);
}
