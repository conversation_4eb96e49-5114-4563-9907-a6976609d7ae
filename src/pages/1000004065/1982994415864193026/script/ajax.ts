import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

// 获取活动规则
export const getRules = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.get('/common/getRule');
    closeToast();
    return data.replaceAll('\n', '<br/>');
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return '';
};
// 获取活动信息
export const getActivityInfo = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getActivityInfo');
    closeToast();
    return data;
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return '';
};
// 获取专属惊喜信息
export const getStagePrize = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getStagePrize');
    closeToast();
    return data;
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return '';
};
// 获取抽奖奖品列表
export const getPrizeList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getPrizeList');
    closeToast();
    return data || [];
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return '';
};
// 获取任务列表
export const getTaskList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getTaskList');
    closeToast();
    return data || [];
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return '';
};
// 获取我的奖品
export const getUserPrizeRecordList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getUserPrizeRecordList');
    closeToast();
    return data || [];
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return [];
};
// 获取曝光商品
export const getSkuList = async (type: 3 | 7 | 8 | 222 | 333) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getSkuList', { type });
    closeToast();
    return data || [];
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return [];
};
// 获取我的留言
export const getUserMessage = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/94008/getUserMessage');
    closeToast();
    return data || [];
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return [];
};
// 通用上传图片
export const uploadImg = async (formData: FormData) => {
  try {
    // const { data } = await httpRequest.post('/common/uploadImg', formData, {
    //   headers: {
    //     'Content-Type': 'multipart/form-data', // 上传文件时需要设置这个头
    //   },
    // });

    const { data } = {
      data: 'https://img10.360buyimg.com/imgzone/jfs/t1/359326/35/3868/135463/690b2118F20e473ce/3fc55013dbd7db05.png',
    };
    return data || '';
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
};
// 抽奖
export const drawLottery = async () => {
  try {
    // showLoadingToast({
    //   forbidClick: true,
    //   duration: 0,
    // });
    const { data } = await httpRequest.post('/94008/drawLottery');
    // const data = {
    //   activityPrizeId: '1985234109025538049',
    //   couponSkuId: null,
    //   exchangeImg: null,
    //   failReason: null,
    //   freeMultiple: null,
    //   prizeImg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/f-3778388935123648191/image/gyjlyjrrbyiq3uhzhkt1ifg68k86ulbs.png',
    //   prizeName: '5京豆',
    //   prizeType: 2,
    //   result: {
    //     result: true,
    //   },
    //   shareCardRecordId: null,
    //   sortId: 12,
    //   status: 1,
    //   userPrizeId: '1985625174722936833',
    // };
    // const data = {
    //   activityPrizeId: '1985250998837628929',
    //   couponSkuId: null,
    //   exchangeImg: null,
    //   failReason: null,
    //   freeMultiple: null,
    //   prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/351224/8/912/78272/68be366bF2627f281/974b119d6ea13994.png',
    //   prizeName: 'OPPO Reno15｜星光蝴蝶结 限定周边礼盒- 限定',
    //   prizeType: 3,
    //   result: {
    //     result: 'o251104163909041831',
    //   },
    //   shareCardRecordId: null,
    //   sortId: 2,
    //   status: 1,
    //   userPrizeId: '1985627868904751106',
    // };
    // closeToast();
    return data;
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return '';
};
// 发留言
export const sendMessage = async (message: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    await httpRequest.post('/94008/messageTask', { message });
    closeToast();
    return true;
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return false;
};
// 做任务
export const dailyCommonTask = async (taskType: 3 | 4 | 5 | 7 | 111 | 888, isShowLoading = true) => {
  try {
    isShowLoading &&
      showLoadingToast({
        forbidClick: true,
        duration: 0,
      });
    await httpRequest.post('/94008/dailyCommonTask', { taskType });
    isShowLoading && closeToast();
    return true;
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return false;
};
// 保存地址
export const userAddressInfo = async (form: any) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    await httpRequest.post('/94008/userAddressInfo', { ...form });
    closeToast();
    return true;
  } catch (error: any) {
    if (error && error?.message) {
      showToast(error?.message);
    } else if (typeof error === 'string') {
      showToast(error);
    } else {
      showToast('请求失败');
    }
  }
  return false;
};
export const fellowShop = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    await httpRequest.post('/common/followShop');
    closeToast();
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
// 下单任务
export const orderSku = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    const res = await httpRequest.post('/94008/orderSku');
    closeToast();
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
// 下单1元锁权权益包任务
export const rightOrderSku = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    const res = await httpRequest.post('/94008/rightOrderSku');
    closeToast();
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
// 分享助力
export const shareUser = async (shareUserId: string) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    const res = await httpRequest.post('/94008/shareUser', { shareUserId });
    closeToast();
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
// 图像分割
export const segmentBody = async (photoUrl: string) => {
  try {
    // const { data } = await httpRequest.post('/94008/segmentBody', { photoUrl });
    const { data } = {
      data: 'http://vibktprfx-prod-prod-damo-eas-cn-shanghai.oss-cn-shanghai.aliyuncs.com/segment-body/2025-11-06/b29072ff-d38e-4202-8cc9-256b7e959d10/image.png?Expires=1762396131&OSSAccessKeyId=LTAI4FoLmvQ9urWXgSRpDvh1&Signature=HvhiTPpjfNqRZNsnBe%2BQZMC63B0%3D',
    };
    return data;
  } catch (error: any) {
    if (error.message) {
      showToast(error.message);
    }
    return '';
  }
};
