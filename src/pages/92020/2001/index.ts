import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
// import '@/style/vantStyle';
// import 'vant/lib/index.css';
import '@/style';
import { httpRequest } from '@/utils/service';
import { showToast } from 'vant';
import { getActData } from './ts/logic';
import OpenCard from './components/OpenCard.vue';
import { gotoErrorPage } from '@/utils/errorHandler';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  thresholdPopup: OpenCard,
  disableNotice: true,
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  try {
    await getActData();
    app.mount('#app');
  } catch (error) {
    gotoErrorPage();
  }
});
