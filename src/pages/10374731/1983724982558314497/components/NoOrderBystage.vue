<!-- 没有订单 弹窗 -->
<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false">
    <div class="dialog">
      <Icon class="close_icon" name="close" size="40" color="#E8C276" @click="closeDialog" />
      <div class="info">根据您注册的信息，系统判断您符合源悦{{ stage + 1 }}段资格，<br />快去购买吧~</div>
      <div class="btn" @click="gotoSkuPage(skuInfo.skuId)">立即购买 &gt;</div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { gotoSkuPage } from '@/utils/platforms/jump';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  skuInfo: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  stage: {
    type: Number,
    required: true,
    default: 0,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
</script>
<style lang="scss" scoped>
.dialog {
  width: 6.5rem;
  overflow: hidden;
  background-image: linear-gradient(to bottom, #fefcf4, #ecd9b2);
  background-size: contain;
  padding: 1.25rem 0.5rem 0.5rem;
  box-sizing: border-box;
  text-align: center;
  color: #6d3e0c;
  border-radius: 0.35rem;
  .info {
    font-size: 0.4rem;
    font-weight: bold;
  }
  .btn {
    background-image: linear-gradient(to bottom, #f6e5ba, #d69a2a);
    font-size: 0.4rem;
    font-weight: bold;
    border: 0.05rem solid #fff;
    border-radius: 0.15rem;
    padding: 0.2rem 0;
    box-sizing: border-box;
    margin-top: 0.2rem;
  }
  .close_icon {
    width: 1rem;
    height: 1rem;
    position: absolute;
    right: 0.2rem;
    top: 0.2rem;
  }
}
</style>
