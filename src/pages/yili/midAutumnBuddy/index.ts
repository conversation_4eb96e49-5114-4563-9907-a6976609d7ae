import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import { getActivityInfo, getOpenCardTask, getOrderTask } from './hooks';
import { closeToast, showLoadingToast } from 'vant';
import dayjs from 'dayjs';

initRem();

window.jmfe.configNavigationBar({
  supportTran: '0',
});

if (dayjs().isAfter(dayjs('2025-10-03 23:59:59'))) {
  // 活动已结束
  // 从链接上获取参数
  const urlParams = new URLSearchParams(window.location.search);
  const adSource = urlParams.get('adSource') || '';
  window.location.replace(`${process.env.VUE_APP_HOST}yili/midAutumnBuddyEnd/?adSource=${adSource}`);
} else {
  const app = createApp(index);
  // 初始化页面
  const config: InitRequest = {
    disableThresholdPopup: true,
    backActRefresh: false,
    disableNotice: true,
    // disableShare: true,
    shopId: '1000013402',
    activityMainId: '1962450685376659458',
    errorPageUrl: `${process.env.VUE_APP_HOST}yili/midAutumnBuddy/errorPage/`,
  };
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
    // 设置页面title
    document.title = baseInfo.activityName;
    Promise.all([getOpenCardTask(), getOrderTask()]);
    await getActivityInfo();
    app.provide('baseInfo', baseInfo);
    app.provide('pathParams', pathParams);
    app.provide('userInfo', userInfo);
    app.use(EventTrackPlugin, {});
    closeToast();
    app.mount('#app');
  });
}
