<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img
        :src="
          furnish.actBg ??
          'https:https://img10.360buyimg.com/imgzone/jfs/t1/301423/11/18784/601352/6862381eF7c157842/845d970e4c4050d4.png'
        "
        alt=""
        class="kv-img"
      />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--{{ shopName }}-->
        </div>
        <div>
          <div
            class="header-btn"
            v-click-track="'hdgz'"
            :style="furnishStyles.headerBtnRules.value"
            @click="showRulePopup"
          />
          <div
            class="header-btn"
            v-click-track="'wdjp'"
            :style="furnishStyles.headerBtnMyPrizes.value"
            @click="showMyPrizePop"
          />
        </div>
      </div>
    </div>

    <!-- 下单权益 -->
    <div class="qyDivTwoAll" :style="{ backgroundImage: `url(${furnish.qy2Bg})` }">
      <!-- <img :src="furnish.qy2Bg" alt="" /> -->
      <div class="qyTwoListDiv">
        <div class="qyTwoItemDiv" v-for="(it, index) in prizeList1" :key="index" :prize-id="it.prizeId">
          <div class="restNum">剩余数量：{{ it.remainingCount }}</div>
          <!-- qyTwoBtn -->
          <div
            :class="[it.status === 2 ? 'qyTwoBtn' : 'qyTwoBtnGray']"
            @click="getQyClick(1, it)"
          >
            立即领取
          </div>
        </div>
      </div>
    </div>

    <!-- 会员老客权益 -->
    <div class="qyDivOneAll" v-click-track="'hyqy'" :style="{ backgroundImage: `url(${furnish.qy1Bg})` }">
      <!-- 立即领取按钮如果符合首购条件则显示，否则置灰 -->
      <div
        v-if="prizeList2[0] && prizeList2[0].status === 2"
        class="getQyBtn"
        @click="getQyClick(2, prizeList2[0])"
      >
        立即领取
      </div>
      <div
        v-else-if="prizeList2[0] && prizeList2[0].status === 1"
        class="getQyBtn getQyBtnGray"
        @click="getQyClick(2, prizeList2[0])"
      >
        已领取
      </div>
      <div
        v-else-if="prizeList2[0] && prizeList2[0].status === 3"
        class="getQyBtn getQyBtnGray"
        @click="getQyClick(2, prizeList2[0])"
      >
        立即领取
      </div>
    </div>

    <div class="qyDivThreeAll">
      <!-- 动态生成的热区按钮 -->
      <HotZone :width="6.88" :data="furnish.hotZoneSetting" reportKey="" />
    </div>

    <div class="sku" v-if="skuList.length">
      <img class="sku-list-img" :src="furnish.showSkuBg" alt="" />
      <div class="sku-list">
        <div
          class="sku-item"
          v-for="(item, index) in skuList"
          :key="index"
          v-click-track="{ code: 'ljgm', value: item.skuId }"
          @click="gotoSkuPage(item.skuId)"
        ></div>
      </div>
    </div>

    <!-- 活动门槛 -->
    <Threshold
      :showPopup="showLimit"
      @closeDialog="showLimit = false"
      :canNotCloseJoin="furnish.canNotCloseJoinPopup"
      :data="baseInfo?.thresholdResponseList"
    />
    <!-- 非会员拦截 -->
    <OpenCard
      :showPopup="showOpenCard"
      @closeDialog="showOpenCard = false"
      :canNotCloseJoin="furnish.canNotCloseJoinPopup"
    />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false" />
    </VanPopup>
    <!--我的奖品-->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize
        v-if="showMyPrize"
        @close="showMyPrize = false"
        @showCardNum="showCardNum"
        @savePhone="''"
      />
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress">
      <SaveAddress
        v-if="showSaveAddress"
        :userReceiveRecordId="userReceiveRecordId"
        @close="showSaveAddress = false"
      />
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    <VanPopup teleport="body" v-if="isShowConfirmPopup" v-model:show="isShowConfirmPopup">
      <GiftConfirm
        :giftInfo="giftItem"
        @close="isShowConfirmPopup = false"
        :multiplePrizeNum="prizeList1.length"
        :multiplePrizeCanReceiveNum="multiplePrizeCanReceiveNum"
        @drawSuccess="drawSuccessFun"
      />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, reactive, ref, watchEffect } from "vue";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { closeToast, showLoadingToast, showToast } from "vant";
import { httpRequest } from "@/utils/service";
import { DecoData } from "@/types/DecoData";
import { BaseInfo } from "@/types/BaseInfo";
import { callShare } from "@/utils/platforms/share";
import { gotoSkuPage } from "@/utils/platforms/jump";
import constant from "@/utils/constant";
import RulePopup from "../components/RulePopup.vue";
import Threshold from "../components/Threshold.vue";
import GiftConfirm from "../components/GiftConfirm.vue";
import OpenCard from "../components/OpenCard.vue";
import SaveAddress from "../components/SaveAddress.vue";
import CopyCard from "../components/CopyCard.vue";
import dayjs from "dayjs";
import MyPrize from "../components/MyPrize.vue";
import HotZone from "../components/HotZone.vue";
import { lzReportClick } from '@/utils/trackEvent/lzReport';

// 奖品列表
const prizeList1 = ref<any>([]); // 权益一奖品列表
const prizeList2 = ref<any>([]); // 权益二奖品列表

const decoData = inject("decoData") as DecoData;
const baseInfo: BaseInfo = inject("baseInfo") as BaseInfo;
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};
// 是否是第一次购买
const isFirstBuy = ref(false);
// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
const showOpenCard = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref("");
// 我的奖品弹窗
const showMyPrize = ref(false);
// 单次领取弹窗
const isShowConfirmPopup = ref(false);

const giftItem = ref<any>({});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);

// 展示门槛显示弹框
const showLimit = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const userReceiveRecordId = ref("");

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get("/common/getRule");
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const showMyPrizePop = () => {
  showMyPrize.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: "",
  prizeImg: "",
  cardDesc: "",
  cardNumber: "",
  cardPassword: "",
  exchangeImg: "",
});
// 展示卡密
const showCardNum = (distribute: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item: any) => {
    cardDetail[item] = distribute[item];
  });
  copyCardPopup.value = true;
};

const multiplePrizeCanReceiveNum = ref(1); // 权益一只能领取一个奖品
const authStatus = ref(2); // 1 新客 2老客
// 主接口获取信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post("/91012/activity");
    // 判断是否开启老客跳新客的逻辑，如果开启并且不是老客则跳转链接
    // 1新客 2老客
    authStatus.value = data.authStatus;
    if (authStatus.value === 1) {
      // 如果是老客
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
    } else {
      lzReportClick('oldCustomer');
    }
    prizeList1.value = data.prizeList.filter((item: any) => item.equityType === 1);
    prizeList2.value = data.prizeList.filter((item: any) => item.equityType === 2);
  } catch (error) {
    console.error(error);
  }
};

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post("/91012/getExposureSku");
    console.log(data, "曝光商品");
    skuList.value = data;
  } catch (error) {
    console.error(error);
  }
};
// 确认领取弹窗的回调
const drawSuccessFun = async (data: any) => {
  isShowConfirmPopup.value = false;
  
  if (data && data.result && data.result.prizeType === 3) {
    // 实物
    showSaveAddress.value = true;
    userReceiveRecordId.value = data.result.userPrizeId;
    getActivityInfo();
  } else if (data && data.result && data.result.prizeType === 7) {
    getActivityInfo();
    // 礼品卡
    copyCardPopup.value = true;
    const cardData = {
      id: data.result.userPrizeId,
      prizeName: data.result.prizeName,
      prizeImg: data.result.prizeImg,
      cardDesc: data.result.result.cardDesc,
      cardNumber: data.result.result.cardNumber,
      cardPassword: data.result.result.cardPassword,
      exchangeImg: data.result.result.exchangeImg,
    };
    showCardNum(cardData);
  } else {
    showToast({
      message: "领取成功",
      duration: 2000,
      forbidClick: true,
      onClose: () => {
        getActivityInfo();
      },
    });
  }
};
// 奖品领取
const getQyClick = async (qyType: any, itemData: any) => {
  // console.log(itemData, prizeList1.value, prizeList2.value, "itemData============");
  console.log(itemData, 'itemData=============');
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    return;
    // console.log("非会员");
  } else if (
    baseInfo.thresholdResponseList[0]?.thresholdCode === 1 ||
    baseInfo.thresholdResponseList[0]?.thresholdCode === 2 ||
    baseInfo.thresholdResponseList[0]?.thresholdCode === 1501
  ) {
    showLimit.value = true;
    return;
  }
  if (itemData.status === 1) {
    showToast("您已经领取过了");
    return;
  }
  if (itemData.remainingCount === 0) {
     showToast("奖品已发光");
    return;
  }
  if (itemData.status === 3) {
    showToast("您不符合参与条件");
    return;
  }
  giftItem.value = itemData;
  if (qyType === 1) {
    // 先进行弹窗确认
    isShowConfirmPopup.value = true;
  } else {
    try {
      showLoadingToast({
        message: "加载中...",
        forbidClick: true,
        duration: 0,
      });
      const { data } = await httpRequest.post("/91012/receivePrize", {
        prizeId: giftItem.value.prizeId,
      });
      showToast({
        message: "领取成功",
        duration: 2000,
        forbidClick: true,
        onClose: () => {
          getActivityInfo();
        },
      });
    } catch (error: any) {
      showToast({
        message: error.message,
        duration: 2000,
        forbidClick: true,
        onClose: () => {
          getActivityInfo();
        },
      });
    }
  }
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    await Promise.all([getActivityInfo(), getSkuList()]);
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log("非会员");
    }
    if (
      baseInfo.thresholdResponseList[0]?.thresholdCode === 1 ||
      baseInfo.thresholdResponseList[0]?.thresholdCode === 2 ||
      baseInfo.thresholdResponseList[0]?.thresholdCode === 1501
    ) {
      showLimit.value = true;
    }
  } catch (error) {
    console.log(error);
  }
};
watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});

const toInit = async () => {
  showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    duration: 0,
  });
  await init();
  closeToast();
};
toInit();
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.2rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.37rem;
    height: 0.57rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.qyDivOneAll {
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/320296/10/12113/43200/68623822Faf5d3658/ba54582c6181f8d7.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.88rem;
  height: 3.5rem;
  margin-left: 50%;
  transform: translateX(-50%);
  position: relative;
  .getQyBtn {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/231990/27/34519/3172/675fcb52F1895689a/96d5dc6d7eba624c.png)
      no-repeat;
    background-size: 100% 100%;
    width: 0.93rem;
    height: 1.9rem;
    display: flex;
    align-items: center;
    color: #fff;
    text-align: center;
    font-size: 0;
    padding: 0.1rem 0.2rem 0.1rem 0.1rem;
    box-sizing: border-box;
    position: absolute;
    right: 0.25rem;
    top: 1.1rem;
  }
  .getQyBtnGray {
    filter: grayscale(1);
  }
}
.qyDivTwoAll {
  width: 6.88rem;
  margin: 0.3rem auto;
  position: relative;
  padding-bottom: 0.2rem;
  height: auto; /* 高度由内容或背景图决定（但需结合其他属性） */
  background-size: 100% auto; /* 宽度100%容器，高度按比例自动适应 */
  background-repeat: no-repeat;
  // background-position: center;
  // min-height: 3.2rem;
  height: 5.7rem;
  img {
    position: absolute;
    width: 6.88rem;
  }
  .qyTwoListDiv {
    position: relative;
    display: flex;
    align-items: center;
    padding: 1.1rem 0.2rem 0 0.2rem;
    margin: 0 auto;
    // justify-content: space-between;
    flex-wrap: wrap;
    .qyTwoItemDiv {
      position: relative;
      width: 2.1rem;
      height: 2.14rem;
      margin: 0 0.02rem;
      .restNum {
        width: 100%;
        text-align: center;
        position: absolute;
        bottom: 0.44rem;
        font-size: 0.18rem;
        color: #212121;
        font-weight: 700;
        left: 50%;
        transform: translateX(-50%);
      }
      .qyTwoBtn {
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/236315/36/9564/5513/658d5abdFd9e2fa3e/597a0717a8f07837.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: absolute;
        // background: #c56f26;
        width: 1.12rem;
        height: 0.33rem;
        font-size: 0;
        // left: 3.15rem;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0.1rem;
      }
      .qyTwoBtnGray {
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/236315/36/9564/5513/658d5abdFd9e2fa3e/597a0717a8f07837.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: absolute;
        // background: #c56f26;
        width: 1.12rem;
        height: 0.33rem;
        font-size: 0;
        // left: 3.15rem;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0.1rem;
        filter: grayscale(1);
      }
    }
  }
}
.qyDivThreeAll {
  margin-top: 0.6rem;
}
.sku {
  width: 7.21rem;
  padding: 1.1rem 0 0.2rem 0;
  position: relative;
  margin: 0.4rem auto 0.1rem auto;
  position: relative;
  // background-color: #000;
  .sku-list-img {
    width: 7.21rem;
    height: auto;
    position: absolute;
    top: 0;
    // background-color: #f2270c;
  }
  .sku-list {
    width: 7.2rem;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    grid-gap: 0.2rem 0.2rem;
    // background-color: aqua;
    position: relative;
    z-index: 1;
    .sku-list1 {
      padding-top: 0.9rem;
      width: 7.25rem;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: auto;
      grid-gap: 0.2rem 0.2rem;
      // background-color: #c56f26;
    }
  }
  .sku-item {
    width: 3.3rem;
    height: 4rem;
    overflow: hidden;
    margin-bottom: 0.2rem;
    .sku-text {
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 0.4rem auto 0;
      box-sizing: border-box;
      .go-sku-btn {
        width: 1.4rem;
        height: 0.3rem;
        //background-color: #000;
      }
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
