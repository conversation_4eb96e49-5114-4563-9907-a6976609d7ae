<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">说说心里话</div>
      <div class="rule-box">
        <VanField v-model="message" placeholder="说说你的心里话....." rows="5" maxlength="50" show-word-limit autosize type="textarea" />
        <img class="btn" @click="handleSend" src="//img10.360buyimg.com/imgzone/jfs/t1/338307/34/27668/13474/69085d6cF045e2e40/11fbe7bd777da43d.png" alt="" srcset="" />
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon, showToast } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { sendMessage } from '../script/ajax';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  message.value = '';
  emits('closeDialog');
};
const opendDialog = async () => {};
const message = ref('');
const handleSend = async () => {
  const result = await sendMessage(message.value);
  console.log('🚀 ~ handleSend ~ resule:', result);
  if (result) {
    showToast('发布完成~');
    emits('closeDialog');
  }
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/346103/9/21713/50108/69085c01F4546bef6/e5c1171bbe8fbd64.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    margin-top: 0.2rem;
    max-height: 6.2rem;
    overflow-y: auto;
    .van-cell {
      background-color: transparent;
      line-height: 2.43;
      color: #7668cb;
      font-size: 0.3rem;
      word-break: break-all;
      --van-field-input-text-color: #7668cb;
    }
    .btn {
      width: 4.24rem;
      margin: 0.5rem auto 0;
    }
  }
}
</style>
