<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">下单商品</div>
      <div class="rule-box">
        <div class="item-box" v-if="skuList.length">
          <div class="list-item" v-for="item in skuList" :key="item.skuMainPicture">
            <img class="sku-img" :src="item.skuMainPicture" alt="" />
            <div class="van-multi-ellipsis--l2 sku-name">{{ item.skuName }}</div>
            <img @click="gotoSkuPage(item.skuId)" class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/349909/40/23750/6169/690c11c0F9181ff00/67bdc8c337443337.png" alt="" />
          </div>
        </div>
        <div class="no-prize" v-else>暂无数据</div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import { getSkuList } from '../script/ajax';
import { gotoSkuPage } from '@/utils/platforms/jump';

const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog', 'success']);
const closeDialog = () => {
  emits('closeDialog');
};
const skuList = ref<
  {
    skuMainPicture: string;
    skuName: string;
    skuId: string;
    isPreviewed: boolean;
  }[]
>([]);
const opendDialog = async () => {
  skuList.value = await getSkuList(8);
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.88rem;
  height: 10.36rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/341530/12/22051/46261/690ab6f0F531f9db2/db73df0ffcccc7e0.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  text-align: center;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    .item-box {
      max-height: 8rem;
      overflow-y: auto;
      margin: 0.3rem auto;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.2rem;
      border-radius: 0.3rem;
      font-family: 'OPPOSansM';
      .list-item {
        background: url(../assets/dialog/sku-bg.png) no-repeat;
        background-size: contain;
        width: 2.88rem;
        height: 3.98rem;
        margin: 0 auto;
        padding: 0.3rem 0.5rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        .sku-img {
          width: 100%;
          height: 2.15rem;
          object-fit: contain;
        }
        .sku-name {
          width: 100%;
          font-size: 0.21rem;
        }
        .btn {
          width: 1.85rem;
          margin: 0 auto;
        }
      }
    }
    .AddCar {
      max-height: 5rem;
    }
    .add-btn {
      width: 4.21rem;
      margin: 0.1rem auto;
    }
    .no-prize {
      font-size: 0.4rem;
      text-align: center;
      margin: 1.5rem auto 0;
    }
  }
}
</style>
