<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">我的奖品</div>
      <div class="rule-box">
        <div class="list-til">
          <div class="list-til-item">时间</div>
          <div class="list-til-item">奖品</div>
          <div class="list-til-item">状态</div>
        </div>
        <div class="item-box" v-if="myPrizes.length">
          <div class="list-item list-til" v-for="item in myPrizes">
            <div class="list-item-text">{{ dayjs(item.createTime).format('MM-DD HH:mm:ss') }}</div>
            <div class="list-item-text">{{ item.prizeName }}</div>
            <div class="list-item-text">
              <div class="address-btn" @click="handlerAddress(item)" v-if="item.prizeType === 3">{{ item.mobile ? '查看地址' : '填写地址' }}</div>
              <div v-else>已发放</div>
            </div>
          </div>
        </div>
        <div class="no-prize" v-else>暂无数据</div>
      </div>
    </div>
  </popup>
  <SaveAddress :address-id="addressId" :addressInfo="addressInfo" :isShowPopup="isShowSaveAddress" @closeDialog="isShowSaveAddress = false" />
</template>

<script setup lang="ts">
import { Popup, Icon, showToast } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getRules, getUserPrizeRecordList } from '../script/ajax';
import dayjs from 'dayjs';
import { MyPrize } from '../script/type';
import SaveAddress from './SaveAddress.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const myPrizes = ref<MyPrize[]>([]);
const opendDialog = async () => {
  console.log('🚀 ~ opendDialog ~ opendDialog:');
  myPrizes.value = await getUserPrizeRecordList();
};

const addressId = ref('');
const isShowSaveAddress = ref(false);
const addressInfo = ref<any>({});
// 中奖已超过1小时，无法填写地址
const handlerAddress = (item: MyPrize) => {
  console.log('🚀 ~ handlerAddress ~ item:', item);
  console.log('🚀 ~ handlerAddress ~ item.createTime:', item.createTime);
  if (dayjs(item.createTime).add(1, 'hour').isBefore(dayjs()) && !item.mobile) {
    showToast('中奖已超过1小时，无法填写地址');
    return;
  }
  addressId.value = item.addressId;
  addressInfo.value = item;
  isShowSaveAddress.value = true;
  closeDialog();
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  text-align: center;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle, #4a3789 0%, #ebe9ed 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .rule-box {
    .list-til {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: 'TsangerYuYangT';
      margin: 0.4rem 0 0;
      .list-til-item {
        flex: 1;
        font-weight: bold;
        font-size: 0.3rem;
      }
    }
    .item-box {
      max-height: 5.2rem;
      overflow-y: auto;
      margin: 0.3rem 0.1rem;
      .list-item {
        font-family: 'OPPOSansR';
        background-color: #ffffff;
        border-radius: 0.06rem;
        border: solid 0.01rem #ffffff;
        margin: 0 0 0.1rem;
        padding: 0.17rem 0;
        box-sizing: border-box;
        div {
          flex: 1;
          font-size: 0.22rem;
          color: #543b72;
          .address-btn {
            background-image: linear-gradient(-90deg, #c0b0d8 0%, #9f8cc5 100%);
            border-radius: 0.3rem;
            color: #fff;
            margin: 0 0.3rem;
            line-height: 2;
          }
        }
      }
    }
    .no-prize {
      font-size: 0.4rem;
      text-align: center;
      margin: 1.5rem auto 0;
    }
  }
}
</style>
