import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import App from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import 'animate.css';
import '@/style';
import { setToastDefaultOptions } from 'vant';

// 全局设置loading
setToastDefaultOptions('loading', { forbidClick: true, duration: 0, message: '请稍候' });

initRem();

const app = createApp(App);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};

init(config).then(({ baseInfo, pathParams, userInfo }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '大转盘活动';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('baseUserInfo', userInfo);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
