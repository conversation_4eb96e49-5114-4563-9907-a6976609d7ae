<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">我的留言</div>
      <div class="rule-box">
        <div class="tips">点击对应信封，查看我的留言</div>
        <div class="item-box" v-if="myMessage.length">
          <div class="list-item" v-for="item in myMessage" :key="item.createTime" @click="handleShowDetails(item)">
            <div class="create-time">留言时间：{{ dayjs(item.createTime).format('YYYY年M月D日') }}</div>
          </div>
        </div>
        <div class="no-prize" v-else>暂无数据</div>
      </div>
    </div>
  </popup>
  <PreiviewComment :message-info="chooseMessage" :isShowPopup="isShowPreviewDialog" @closeDialog="isShowPreviewDialog = false"></PreiviewComment>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getRules, getUserMessage } from '../script/ajax';
import dayjs from 'dayjs';
import PreiviewComment from './PreiviewComment.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const myMessage = ref<
  {
    createTime: string;
    message: string;
  }[]
>([]);
const opendDialog = async () => {
  console.log('🚀 ~ opendDialog ~ opendDialog:');
  myMessage.value = await getUserMessage();
};
const isShowPreviewDialog = ref(false);
const chooseMessage = ref<{ createTime: string; message: string }>({
  createTime: '',
  message: '',
});

const handleShowDetails = (item: { createTime: string; message: string }) => {
  chooseMessage.value = item;
  isShowPreviewDialog.value = true;
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  text-align: center;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    font-weight: bold;
    background: radial-gradient(circle, #4a3789 0%, #ebe9ed 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .rule-box {
    font-family: 'OPPOSansR';
    .tips {
      font-size: 0.26rem;
      text-align: center;
      line-height: 2;
    }
    .item-box {
      max-height: 6.1rem;
      overflow-y: auto;
      margin: 0.3rem auto;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0.2rem;
      .list-item {
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/353027/35/5414/27102/69081c0cFf3f6c88e/ae90bb70af47f23d.png) no-repeat;
        background-size: contain;
        width: 1.86rem;
        height: 1.25rem;
        border-radius: 0.06rem;
        border: solid 0.01rem #ffffff;
        margin: 0 auto;
        position: relative;
        .create-time {
          position: absolute;
          bottom: 0.05rem;
          left: 0;
          width: 100%;
          text-align: center;
          font-size: 0.13rem;
          color: #7454af;
          font-style: italic;
        }
      }
    }
    .no-prize {
      font-size: 0.4rem;
      text-align: center;
      margin: 1.5rem auto 0;
    }
  }
}
</style>
