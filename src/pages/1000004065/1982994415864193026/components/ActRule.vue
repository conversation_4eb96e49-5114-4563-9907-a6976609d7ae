<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">活动规则</div>
      <div class="rule-box">
        <div class="rule" v-html="rule"></div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getRules } from '../script/ajax';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const rule = ref('');
const opendDialog = async () => {
  !rule.value && (rule.value = await getRules());
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    margin-top: 0.4rem;
    max-height: 6.2rem;
    overflow-y: auto;
  }
  .rule {
    font-size: 0.24rem;
    box-sizing: border-box;
    font-family: 'TsangerYuYangT';
  }
}
</style>
