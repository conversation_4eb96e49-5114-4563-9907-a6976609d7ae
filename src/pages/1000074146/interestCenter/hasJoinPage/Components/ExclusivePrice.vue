<template>
  <div class="main" :style="{ backgroundImage: `url(${giftPackage.bg})` }">
    <img :src="giftPackage.titleImg" alt="" class="title-img" />
    <ScrollContainer class="sku-scroll">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in giftPackage.prizeList" :key="index" :style="{ backgroundImage: `url(${giftPackage.prizeBg})`, color: giftPackage.textColor }" @click="toSku(item)">
          <div class="sku-name">{{ item.prizeName }}</div>
          <!--          <img :src="item.skuMainPicture" alt="" class="sku-img" />-->
          <div class="sku-img" :style="{ backgroundImage: `url(${item.prizeImg})` }"></div>
          <div class="discountedPrize">S+专享价{{ item.discountedPrize }}元</div>
          <div class="originalPrize">原价{{ item.originalPrize }}元</div>
        </div>
      </div>
    </ScrollContainer>
  </div>
</template>

<script setup lang="ts">
import { computed, effect, inject, ref } from 'vue';
import { preview } from '../../Utils';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { showToast } from 'vant';
import ScrollContainer from '../../Components/ScrollContainer.vue';

const MemberInfo = inject('memberInfo') as any;

const props = defineProps(['decorationInfo']);
const decoData = computed(() => props.decorationInfo);

let initialLevel = 2;
if (!preview && MemberInfo.vipLevel > 2) {
  initialLevel = MemberInfo.vipLevel;
}
const showLevel = ref(initialLevel);
const giftPackage = computed(() => {
  const item = props.decorationInfo.memberLevels[showLevel.value - 2].giftPackage;
  return item;
});

const toSku = (item: any) => {
  if (preview) return;
  if (showLevel.value !== MemberInfo.vipLevel) {
    showToast('抱歉，您等级不符，升级可享更多福利~');
    return;
  }
  // gotoSkuPage(item.skuId);
  window.location.href = item.linkUrl;
};

effect(() => {
  if (!preview) return;
  const currentLevel = decoData.value.currentLevel ?? 0;
  showLevel.value = currentLevel + 2;
});
</script>

<style scoped lang="scss">
.main {
  position: relative;
  width: 7rem;
  margin: 0 auto;
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.title-img {
  width: 100%;
  height: 0.71rem;
  object-fit: contain;
  margin-bottom: 0.47rem;
}
.sku-scroll {
  width: 100%;
  .sku-list {
    width: max-content;
    min-height: 100%;
    padding: 0 0.2rem;
    display: flex;
    justify-content: space-evenly;
    .sku-item {
      width: 2rem;
      height: 2.8rem;
      margin-right: 0.15rem;
      padding-top: 0.15rem;
      background-size: 100%;
      background-repeat: no-repeat;
      text-align: center;
      .sku-name {
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 0.16rem;
        line-height: 0.16rem;
        margin-bottom: 0.05rem;
        padding: 0 0.1rem;
      }
      .sku-img {
        width: 1.9rem;
        height: 1.75rem;
        margin: 0 auto;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
      .discountedPrize {
        font-size: 0.18rem;
        line-height: 0.36rem;
        height: 0.36rem;
        font-weight: bold;
        text-align: center;
        white-space: nowrap;
      }
      .originalPrize {
        font-size: 0.14rem;
        line-height: 0.3rem;
        height: 0.3rem;
        text-align: center;
        width: 1rem;
        white-space: nowrap;
        // 文字中间加横线
        text-decoration: line-through;
      }
    }
  }
}
</style>
