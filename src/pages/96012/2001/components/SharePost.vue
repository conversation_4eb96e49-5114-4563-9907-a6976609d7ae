<template>
  <div class="sharePosterDivAll">
    <div class="closeDiv" @click="closeClick()"></div>
    <div class="sharePosterDiv" id="sharePosterDivId" v-if="!posterUrl">
      <div class="messageDiv">
        <img class="avatarImg" :src="userInfo.avatar" alt=""></img>
        <div class="nicknameDiv">{{userInfo ? maskName(userInfo.nickname,4) : ''}}</div>
      </div>
      <div class="shareImgDiv">
        <img class="shareImg" :src="shareData.shareImg" alt=""></img>
      </div>
      <div class="imgMessageDiv">
        <div class="imgMessageleftDiv">
          <div class="shareTitleDiv">{{shareData.shareTitle}}</div>
          <div class="shareContentDiv">{{shareData.shareContent}}</div>
        </div>
        <div class="imgMessageRightDiv">
          <img class="qrcodeImg" :src="qrcodeImg" alt=""></img>
          <div class="textDiv">使用京东app扫码 为我点赞吧~</div>
        </div>
      </div>
    </div>
    <div v-else-if="posterUrl" class="posterImgDiv">
      <img :src="posterUrl" alt=""></img>
    </div>
  </div>
</template>
<script lang="ts" setup>
import constant from "@/utils/constant";
import { inject, ref } from "vue";
import QRCode from "qrcode";
import html2canvas from "html2canvas";
import { closeToast, showLoadingToast } from "vant";

const isJDApp = ref<boolean>(window.jmfe.isApp('jd'));
const userInfo = inject("userInfo") as any;
console.log(userInfo, "userInfo========");
const posterUrl = ref('');
const savePoster = ref<any>(null);
const props = defineProps({
  shareData: {
    type: Object,
    default: {
      id: "123123",
      shareImg:
        "https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png",
      shareTitle: "标题标题1标题1标题1标题11asd啊实打实大苏打",
      shareContent: "阿斯顿哈哈的骄傲和沙发嘎嘎发就爱国发阿斯顿哈哈的骄傲和沙发嘎嘎发就爱国发asd",
    },
  },
});
const emits = defineEmits(["close"]);
const closeClick = () => {
  emits("close");
};

const shareConfig = JSON.parse(
  window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? ""
);
const shareId = ref<string>(shareConfig.shareId);
// console.log(shareId, "shareId=========");
const qrcodeImg = ref("");
const getPoster = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const element:any = document.getElementById('sharePosterDivId');
    // 核心转换逻辑
    const canvas = await html2canvas(element, {
      scale: 3, // 提高输出质量（3倍）
      logging: true, // 开启日志
      useCORS: true, // 解决图片跨域问题
      backgroundColor: null // 设置背景色
    });
    // 转换为图片
    const imgData = canvas.toDataURL('image/png');
    posterUrl.value = imgData;
    savePoster.value = 1;
    closeToast();
  } catch (error: any) {
    closeToast();
    savePoster.value = 0;
    console.error(error, '生成海报失败========');
  } 
};
const getCode = async () => {
  QRCode.toDataURL(`${window.location.href}&shareId=${shareConfig.shareId}`)
    .then((url: string) => {
      qrcodeImg.value = url;
      setTimeout(() => {
        getPoster();
      },1000)  
    })
    .catch((err: any) => {
      console.error(err);
    });
};
const maskName = (str: string, maskLength = 4) => {
  const maskStr = Array.from(Array(maskLength), () => "*").join("");
  if (str && str.length >= 2) {
    return `${str.substr(0, 1)}${maskStr}${str.substr(str.length - 1, 1)}`;
  }
  return str;
};
getCode();
</script>
<style lang="scss" scoped>
.sharePosterDivAll {
  position: relative;
  padding-top: 1.42rem;
  // height: 10.48rem;
  .sharePosterDiv {
    background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/289959/23/17305/8746/690b1117F587363d9/83acdc90108456e5.png);
    background-size: 100% 100%;
    width: 6.5rem;
    height: 10.11rem;
    background-repeat: no-repeat;
    position: relative;
    .messageDiv {
      display: flex;
      align-items: center;
      padding: 0.3rem 0.32rem;
      .avatarImg {
        border-radius: 50%;
        width: 0.8rem;
        height: 0.8rem;
      }
      .nicknameDiv {
        font-size: 0.3rem;
        color: #000;
        margin-left: 0.2rem;
      }
    }
    .shareImgDiv {
      border-radius: 0.15rem;
      width: 5.9rem;
      height: 5.9rem;
      object-fit: contain;
      margin-left: 50%;
      transform: translateX(-50%);
      .shareImg {
        width: 100%;
        height: 100%;
        border-radius: 0.15rem;
      }
    }
    .imgMessageDiv {
      display: flex;
      justify-content: space-between;
      width: 5.9rem;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 0.2rem;
      .imgMessageleftDiv {
        max-width: 3.6rem;
        .shareTitleDiv {
          font-size: 0.3rem;
          color: #000;
        }
        .shareContentDiv {
          font-size: 0.24rem;
          height: 0.8rem;
          color: #666666;
          overflow: hidden;
          display: -webkit-box; /* 必须结合 -webkit-box 布局 */
          -webkit-box-orient: vertical; /* 设置垂直方向排列 */
          -webkit-line-clamp: 2; /* 限制显示行数为2行 */
          overflow: hidden; /* 隐藏超出内容 */
          text-overflow: ellipsis; /* 显示省略号 */
        }
      }
      .imgMessageRightDiv {
        width: 1.65rem;
        height: 1.65rem;
        .qrcodeImg {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .textDiv {
          font-size: 0.22rem;
          color: #666666;
        }
      }
    }
  }
  .posterImgDiv{
    width: 6.5rem;
    height: 10.11rem;
    img{
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .closeDiv {
    position: absolute;
    right: 0rem;
    top: 1rem;
    width: 0.8rem;
    height: 0.8rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/348764/34/22506/2193/6909729cFdcc4a42c/569198a303056014.png);
    background-repeat: no-repeat;
    background-size: 100%;

    // background-color: red;
  }
}
</style>
