import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, initPreview } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';
import { convertStringsToJSON, preview } from '../Utils';
import { decorationInfo, popupDecorationInfo, upDataPrize } from './DataHooks';
import CLIENT_TYPE, { getClientType, isPC } from '@/utils/platforms/clientType';
import constant from '@/utils/constant';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

initRem();
const notJoinPageUrl = `${process.env.VUE_APP_HOST}1000074146/interestCenter/notJoinPage/`;
const app = createApp(root);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  activityMainId: '1830542371332141058',
  shopId: '1000074146',
  activityType: '2',
};

const getDecorationData = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/decorationData', {
      pageType: '2',
    });
    const resultMap: any = {};
    data.forEach((item: any) => {
      resultMap[item.keyName] = item.decorationData;
    });
    decorationInfo.value = convertStringsToJSON(resultMap);
    await Promise.all([upDataPrize('upgradeAdvancedLevel'), upDataPrize('firstPurchaseGift'), upDataPrize('exclusivePrice'), upDataPrize('medalRedBag'), upDataPrize('birthdayGift')]);
  } catch (error: any) {
    console.error(error);
  }
};
const getPopupDeco = async () => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/decorationData', {
      pageType: '4',
    });
    const resultMap: any = {};
    data.forEach((item: any) => {
      resultMap[item.keyName] = item.decorationData;
    });
    popupDecorationInfo.value = convertStringsToJSON(resultMap);
    popupDecorationInfo.value.thresholdPage.saveAddressAfter = popupDecorationInfo.value.thresholdPage.saveAddress || {};
  } catch (error: any) {
    console.error(error);
  }
};
// 获取会员类型
const getMemberInfo = async (adsource: string) => {
  try {
    const { data } = await httpRequest.post('/swisseMemberCenter/memberInfo');
    if (data.vipLevel > 0) {
      app.provide('memberInfo', data);
      lzReportClick({ code: 'isMember' });
      await Promise.all([getDecorationData(), getPopupDeco()]);
    } else {
      window.location.href = `${notJoinPageUrl}?adsource=${adsource}`;
    }
  } catch (error: any) {
    window.location.href = `${notJoinPageUrl}?adsource=${adsource}`;
  }
};

function isMobileBrowser() {
  // 定义常见移动设备的关键字
  const mobileKeywords = ['Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 'Windows Phone', 'Opera Mini', 'IEMobile'];

  // 遍历关键字数组，判断 userAgent 中是否包含这些关键字
  return mobileKeywords.some((keyword) => navigator.userAgent.includes(keyword));
}

const isApp = window.jmfe.isApp('jd');
const isWechat = getClientType() === CLIENT_TYPE.WECHAT;

if (preview) {
  initPreview(config).then(async () => {
    app.provide('memberInfo', { vipLevel: 1 });
    app.mount('#app');
  });
} else if (!isApp && !isWechat && (isPC() || isMobileBrowser()) && process.env.NODE_ENV === 'production' && !window.location.href.includes('debug')) {
  const accessUrl = encodeURIComponent(window.location.href);
  window.location.href = `https://lzkjdz-isv.isvjcloud.com/prod/cc/custom/landing/openAppPage2/?actlink=${accessUrl}`;
} else {
  init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
    await getMemberInfo(pathParams?.adsource ?? 'default');
    document.title = baseInfo.activityName;
    app.provide('baseInfo', baseInfo);
    app.provide('userInfo', userInfo);
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, {});
    app.mount('#app');
  });
}
