import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import constant from '@/utils/constant';
import App from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import 'animate.css';
import '@/style';
import { setToastDefaultOptions, allowMultipleToast, IndexAnchor, IndexBar } from 'vant';
import store from './store/index';
import { getInitData } from './config/api';
import initializeRouter from './router';

// 全局设置loading toast配置
setToastDefaultOptions('loading', {
  forbidClick: true,
  duration: 0,
  message: '请稍候',
});
// 全局设置 普通toast配置
setToastDefaultOptions({
  duration: 2000,
  forbidClick: true,
});
// 允许多个toast同时展示
allowMultipleToast();
// 页面自适应
initRem();

const app = createApp(App);
window.document.title = '幼犬体重日记';
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  shopId: '1000000844',
  activityMainId: '1905108599715110914',
};
function getUrlVars(): any {
  const vars: any = {};
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, (m, key, value) => {
    if (key === 'adSource') {
      key = 'adsource';
    }
    vars[key] = value;
  });
  return vars;
}
const getShareId = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('userKey');
};

const { userKey, debug } = getUrlVars();
const sessionUserKey = sessionStorage.getItem('WEISI_USER_KEY');

if (debug) {
  const vc = new window.VConsole();
}
const userId = userKey || sessionUserKey;
if (userId) {
  getInitData({
    activityMainId: '1914166231830093826',
    userId,
  }).then((res: any) => {
    console.log(res);
    if (res.data) {
      sessionStorage.setItem(constant.LZ_JD_ENCRYPT_PIN, res.data.encryptPin);
      sessionStorage.setItem(constant.LZ_PIN_TOKEN, res.data.pinToken);
      sessionStorage.setItem(constant.LZ_WHITE_USER, `${res.data.isWhiteUser}`);
      sessionStorage.setItem(constant.LZ_ACTIVITY_ID, '1914166231830093826');
      sessionStorage.setItem('WEISI_USER_KEY', userId);
      app.provide('baseInfo', {});
      app.provide('pathParams', getUrlVars());
      app.provide('baseUserInfo', res.data);
      app.use(EventTrackPlugin, {});

      const router = initializeRouter();
      app.use(router);
      // @ts-ignore
      app.use(store);
      app.use(IndexAnchor);
      app.use(IndexBar);
      app.mount('#app');
    } else {
      window.location.href = constant.LZ_LOST_PAGE_URL;
    }
  });
} else {
  window.location.href = constant.LZ_LOST_PAGE_URL;
}
