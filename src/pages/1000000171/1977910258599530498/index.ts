/**
 * @Description:wuhao
 * @Date: 2025年5月28日
 * @Description:万和打榜抽奖互动
 */
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, checkStatus } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem(750);

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};
init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
  checkStatus(baseInfo, true, true);
  // 设置页面title
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('userBaseInfo', userInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
