<template>
  <div>
    <div class="close-box">
      <img alt=""  src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
      </div>
    <div class='guide'>
      <img class="guide-bg" src="//img10.360buyimg.com/imgzone/jfs/t1/353897/31/6826/58823/690b050eF4d0166c0/0bfa30948b31dd3f.png" alt="">
      <div class="guide-content">
        <div v-html="guide" style="white-space: pre-wrap; word-break: break-all;"></div>
      </div>

   </div>
  </div>
</template>

<script lang='ts' setup>
const props = defineProps({
  guide: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang='scss'>
.guide {
  width: 80%;
  margin: 0 auto;
  position: relative;
  .guide-bg {
    width: 100%;
    object-fit: contain;
  }
  .guide-content {
    overflow-y: auto;
    width: 100%;
    height: 5.3rem;
    box-sizing: border-box;
    padding: 0 0.4rem;
    position: absolute;
    top: 1.2rem;
    color: #7c2929;
    word-wrap: break-word;
  }

}
.close-box {
  display: flex;
  width: 80%;
  margin: 0 auto;
  justify-content: flex-end;
  .close {
    border: 1px solid #fff;
    padding: 0.1rem;
    border-radius: 50%;
  }
}
</style>
