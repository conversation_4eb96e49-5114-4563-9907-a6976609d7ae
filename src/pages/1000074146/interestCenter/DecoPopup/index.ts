import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { initPreview } from '@/utils';

initRem();
const app = createApp(root);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  activityMainId: '1830542371332141058',
  shopId: '1000074146',
  activityType: '2',
};

initPreview(config).then(async () => {
  app.provide('memberInfo', { vipLevel: 1 });
  app.mount('#app');
});
