import { createApp } from 'vue';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

function initRem(uiPageWidth = 750, rate = 100): void {
  const docEl: HTMLElement = document.documentElement;
  const bodyEl: HTMLElement = document.body;
  const recalc = () => {
    const {
      clientWidth,
    } = docEl;

    docEl.style.fontSize = `${rate * (clientWidth / uiPageWidth)}px`;
    // 重制body的字号为16px，避免html的字号对px的影响
    // 总感觉这段代码会有问题
    bodyEl.style.fontSize = '16px';
  };

  document.addEventListener('DOMContentLoaded', recalc, false);
  window.onresize = recalc;
  recalc();
}
initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  shopId: '1000009821',
  // shopId: '734259',
  // activityMainId: '1788096771846885378',
  // shopId: '734259',
  // activityMainId: '1788096771846885378',
};

init(config).then(({ baseInfo, pathParams }) => {
  // 设置页面title
  document.title = '直播间下单,好礼送不停';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
