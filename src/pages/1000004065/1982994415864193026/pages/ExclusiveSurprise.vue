<!--
* @Description: caoshijie
* @Date: 2025-10-28 20:06:32
* @Description: 专属惊喜
* @FilePath: src\pages\1000004065\1982994415864193026\components\ExclusiveSurprise.vue 
-->
<template>
  <div class="surprise">
    <div class="star-light">
      当前已集结星光值：<span>{{ stageInfo.totalStarValue }}</span>
    </div>
    <div class="tips">汇聚守护星光值，全网总星光值达到指定数量，将解锁对应奖品加入奖池</div>
    <div class="gift-list">
      <div class="gift-item" v-for="(item, index) in stageInfo.stagePrizeList">
        <img v-if="index >= currentStageIndex" class="lock-icon" src="../assets/surprise/lock.png" alt="" />
        <img v-else class="lock-icon" src="../assets/surprise/unlock.png" alt="" />
        <img class="gift-img" :src="item.prizeImg" alt="" srcset="" />
        <div class="gift-name">{{ item.prizeName }}</div>
      </div>
    </div>
    <div class="progress-container">
      <div class="progress">
        <div class="progress-bar" :style="{ width: `${progressWidth}rem` }"></div>
        <img class="star-icon" :style="{ left: `${+progressWidth - 0.2}rem` }" src="../assets/surprise/star.png" alt="" />
        <div class="bowknot-container">
          <div v-for="(item, index) in stageInfo.stagePrizeList">
            <img :class="{ 'btn-disabled': index >= currentStageIndex }" src="../assets/surprise/bowknot.png" alt="" srcset="" />
            <div class="bowknot-text">{{ Math.floor(item.starValue / 10000) }}W星光值</div>
          </div>
        </div>
        <img class="bottle" :src="require(`../assets/surprise/bottle-${currentStageIndex}.png`)" alt="" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { getStagePrize } from '../script/ajax';
import type { StagePrizeList } from '../script/type';

const stageInfo = reactive<StagePrizeList>({
  stagePrizeList: [],
  totalStarValue: 0,
});
// 添加一个响应式字段来存储当前阶段数
const currentStageIndex = ref(0);

const progressWidth = computed(() => {
  // 每个阶段的最大宽度限制：1.2rem, 3.25rem, 6.2rem
  const maxWidth = 6.2;
  const stageMaxWidths = [1.2, 3.25, 5.5];
  const totalStarValue = stageInfo.totalStarValue;
  const stagePrizeList = stageInfo.stagePrizeList;

  if (stagePrizeList.length === 0) {
    currentStageIndex.value = 0;
    return '0';
  }

  // 找到当前星光值所在的区间
  let currentIndex = stagePrizeList.findIndex((item) => totalStarValue < item.starValue);

  // 如果没找到，说明超过了最大值，使用最后一个区间
  if (currentIndex === -1) {
    currentIndex = stagePrizeList.length - 1;
    currentStageIndex.value = currentIndex;
    return `${maxWidth}`;
  }

  // 将当前阶段数赋值给外部字段
  currentStageIndex.value = currentIndex;

  // 计算当前区间的比例位置
  let position = 0;

  if (currentIndex === 0) {
    // 第一个区间，从0开始
    const endValue = stagePrizeList[0].starValue;
    const ratio = Math.min(totalStarValue / endValue, 1);
    position = ratio * stageMaxWidths[0];
  } else {
    // 其他区间，从前一个节点开始
    const startValue = stagePrizeList[currentIndex - 1].starValue;
    const endValue = stagePrizeList[currentIndex].starValue;
    const ratio = Math.max(0, Math.min(1, (totalStarValue - startValue) / (endValue - startValue)));

    // 根据当前阶段应用相应的最大宽度限制
    const prevStageMaxWidth = stageMaxWidths[Math.min(currentIndex, stageMaxWidths.length - 1) - 1];
    const currentStageMaxWidth = stageMaxWidths[Math.min(currentIndex, stageMaxWidths.length - 1)];
    const stageWidth = currentStageMaxWidth - prevStageMaxWidth;
    position = prevStageMaxWidth + ratio * stageWidth;
  }

  return `${Math.max(0, position)}`;
});

onMounted(async () => {
  const result = await getStagePrize();
  if (!result) return;
  Object.assign(stageInfo, result);
  //   stageInfo.totalStarValue = 8888888;
});
</script>
<style lang="scss" scoped>
.surprise {
  background: url(../assets/surprise/surprise.png) no-repeat;
  background-size: contain;
  width: 7.5rem;
  height: 6.67rem;
  position: relative;
  padding: 2.15rem 0.34rem 0.2rem;
  box-sizing: border-box;
  .star-light {
    font-size: 0.24rem;
    position: absolute;
    margin-left: 2.1rem;
    font-family: 'OPPOSansR';
    span {
      font-family: 'OPPOSansH';
      font-size: 0.26rem;
      color: #6c6aa4;
      vertical-align: middle;
    }
  }
  .tips {
    font-family: 'OPPOSansR';
    font-size: 0.2rem;
    color: #4b2179;
    text-align: center;
    width: 100%;
    margin-top: 0.4rem;
  }
  .gift-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0.1rem 0.1rem;
    gap: 0.2rem;
    .gift-item {
      background: url(../assets/surprise/gift-bg.png) no-repeat;
      background-size: contain;
      padding: 0.35rem 0.3rem;
      box-sizing: border-box;
      width: 2.14rem;
      height: 2.36rem;
      position: relative;
      .lock-icon {
        width: 0.54rem;
        position: absolute;
        left: -0.2rem;
        top: 0.05rem;
      }
      .gift-img {
        width: 1rem;
        height: 1.3rem;
        object-fit: contain;
        margin: 0 auto;
      }
      .gift-name {
        font-size: 0.18rem;
        width: 1.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 0.1rem;
      }
    }
  }
  .progress-container {
    padding-top: 0.3rem;
    margin: 0.1rem 0.2rem 0;
    box-sizing: border-box;
    .progress {
      width: 96%;
      margin-left: 0.1rem;
      height: 0.15rem;
      background-color: #dedede;
      border-radius: 0.07rem;
      border: solid 0.01rem #c0b3fb;
      border-radius: 0.07rem;
      border: solid 0.01rem #c0b3fb;
      box-sizing: border-box;
      position: relative;
      .star-icon {
        width: 0.3rem;
        position: absolute;
        top: -0.1rem;
        left: 0;
        transform: rotate(-35deg);
      }
      .progress-bar {
        width: 0;
        border-radius: 0.07rem;
        height: 100%;
        background: url(../assets/surprise/progress-bg.png) repeat-x;
        background-size: contain;
      }
      .bowknot-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: -0.35rem 0.53rem;
        position: relative;
        z-index: 1;
        img {
          width: 0.82rem;
          margin: 0 auto;
        }
        .bowknot-text {
          text-align: center;
          font-size: 0.18rem;
          color: #4b2179;
          font-family: 'OPPOSansM';
          line-height: 2.5;
        }
      }
      .bottle {
        width: 0.54rem;
        position: absolute;
        right: -0.3rem;
        top: -0.4rem;
      }
    }
  }
}
</style>
