<template>
  <div v-if="!showUpload">
    <div class="basePage" :style="furnishStyles.pageBg.value">
      <HomePage v-if="currentPage === 0" :periodList="periodList" :sectionInfo="sectionInfo" :ruleText="ruleText" :taskList="taskList"/>
      <ExamplePage v-else-if="currentPage === 1" :officialTabs="officialTabs"/>
      <RankingPage v-else-if="currentPage === 2" :rankPrizesList="rankPrizesList" :ruleText="rankRuleText"/>
      <MyPage v-else-if="currentPage === 3" :mineInfo="mineInfo"/>
    </div>
    <div class="navBar">
      <van-tabbar v-model="active" class="tabbarBox" :style="furnishStyles.bottomNavImgBg.value">
        <img @click="uploadClick" class="uploadIcon" :src="furnish.uploadIcon" alt="">
        <van-tabbar-item v-for="(item, index) in iconList" :key="index" @click="switchPage(index)">
          <template #icon="">
            <img style="height:0.82rem" :src="active === index ? item.icon.active : item.icon.inactive" />
          </template>
        </van-tabbar-item>
      </van-tabbar>
    </div>
  </div>
  <UploadPage v-else />
</template>

<script lang="ts" setup>
import {inject, onMounted, reactive, ref} from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import 'swiper/swiper.min.css';
import RankingPage from "./components/RankingPage.vue";
import ExamplePage from "./components/ExamplePage.vue";
import MyPage from "./components/MyPage.vue";
import HomePage from "./components/HomePage.vue";
import UploadPage from "./components/UploadPage.vue";
import { Task } from '../ts/type';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const showUpload = ref(false);

// 上传按钮点击
const uploadClick = () => {
  showUpload.value = true;
};
// 当前显示的页面
const currentPage = ref(0)
// 当前高亮的tab
const active = ref(0);
// 切换页面
const switchPage = (pageKey: number) => {
  active.value = Number(pageKey);
  currentPage.value = Number(pageKey);
}

const iconList = [
  {
    icon: {
      active:'https://img10.360buyimg.com/imgzone/jfs/t1/338411/4/23148/1123/68faf3efF92483d0d/5decc5a423023b87.png',
      inactive: 'https://img10.360buyimg.com/imgzone/jfs/t1/340119/24/26894/1455/690457b4Fc7c471c9/9b2e9bc8b83e7e49.png',
    }
  },
  {
    icon: {
      active:'https://img10.360buyimg.com/imgzone/jfs/t1/353906/22/427/2181/68faf3efF872e14d9/3115ba6bbbccf844.png',
      inactive: 'https://img10.360buyimg.com/imgzone/jfs/t1/356497/26/1156/2552/6904777fF9b58d6df/a87925896623b8c3.png',
    }
  },
  {
    icon: {
      active:'https://img10.360buyimg.com/imgzone/jfs/t1/351369/2/17224/1445/68faf3efFff7735c6/2b27e2fd8e66e927.png',
      inactive: 'https://img10.360buyimg.com/imgzone/jfs/t1/337948/26/26112/1665/690457b4F626c696f/1cb2fd11a019ae45.png',
    }
  },
  {
    icon: {
      active:'https://img10.360buyimg.com/imgzone/jfs/t1/102675/16/32378/1627/68faf3efFb504a044/13425fd25264411c.png',
      inactive: 'https://img10.360buyimg.com/imgzone/jfs/t1/349650/38/20092/1949/690457b3Fd672e885/b6b424648fa2c945.png',
    }
  }
];

const shopName = ref('xxx自营旗舰店');
const isLoadingFinish = ref(false);

const ruleText = ref('');
const rankRuleText = ref('');
const taskList = ref([] as Task[]);

const showSelect = ref(false);
// todo
const periodList = ref([
  {
    periodDate: "2025-10-31T02:02:23.000Z",
    lotteryPrizeList: [
      // {
      //   "prizeKey": 33939243,
      //   "prizeType": 2,
      //   "dayLimitType": 1,
      //   "dayLimit": 1,
      //   "prizeImg": "//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png",
      //   "createTime": 1761636211000,
      //   "endDate": 1761895467000,
      //   "planId": 33939243,
      //   "planName": "crx测试编辑1028",
      //   "planStatus": 2,
      //   "quantityFreeze": null,
      //   "quantityRemain": 10,
      //   "quantityTotal": 10,
      //   "quantityUsed": 0,
      //   "startDate": 1761636267000,
      //   "version": 1,
      //   "prizeName": "1京豆",
      //   "unitCount": 1,
      //   "unitPrice": 0.01,
      //   "sendTotalCount": 1,
      //   "sortId": 0,
      //   "status": 1,
      //   "physicalType": 0
      // },
      // {
      //   "prizeKey": "s250922140549509070",
      //   "prizeType": 3,
      //   "dayLimitType": 1,
      //   "dayLimit": 1,
      //   "prizeImg": "https://img10.360buyimg.com/imgzone/jfs/t1/102728/14/53165/4456/68c92930F555fbaec/9b0bd82ab2ec35c1.png",
      //   "activityIds": [],
      //   "createTime": 1758521149000,
      //   "quantityAvailable": 10,
      //   "quantityFreeze": 0,
      //   "quantityPreDeliver": 0,
      //   "quantityRemain": 10,
      //   "quantityTotal": 10,
      //   "shopId": null,
      //   "skuCode": "s250922140549509070",
      //   "skuDetails": null,
      //   "skuMainPicture": "https://img10.360buyimg.com/imgzone/jfs/t1/102728/14/53165/4456/68c92930F555fbaec/9b0bd82ab2ec35c1.png",
      //   "skuName": "桃子9.22实物哈哈哈哈或或或或或或或或",
      //   "version": 1,
      //   "wmsCode": null,
      //   "prizeName": "桃子9.22实物哈哈哈哈或或或或或或或或",
      //   "unitPrice": 1,
      //   "unitCount": 1,
      //   "sendTotalCount": 1,
      //   "sortId": 1,
      //   "status": 1,
      //   "physicalType": 0
      // },
      // {
      //   "prizeKey": "s250916174511472183",
      //   "prizeType": 3,
      //   "dayLimitType": 1,
      //   "dayLimit": 1,
      //   "prizeImg": "https://img10.360buyimg.com/imgzone/jfs/t1/328768/35/19511/5985/68c9319fF18abf52c/728390bfff52eef7.png",
      //   "activityIds": [],
      //   "createTime": 1758015911000,
      //   "quantityAvailable": 8,
      //   "quantityFreeze": 0,
      //   "quantityPreDeliver": 2,
      //   "quantityRemain": 10,
      //   "quantityTotal": 10,
      //   "shopId": null,
      //   "skuCode": "s250916174511472183",
      //   "skuDetails": null,
      //   "skuMainPicture": "https://img10.360buyimg.com/imgzone/jfs/t1/328768/35/19511/5985/68c9319fF18abf52c/728390bfff52eef7.png",
      //   "skuName": "桃下架",
      //   "version": 1,
      //   "wmsCode": null,
      //   "prizeName": "桃下架",
      //   "unitPrice": 1,
      //   "unitCount": 1,
      //   "sendTotalCount": 1,
      //   "sortId": 2,
      //   "status": 1,
      //   "physicalType": 0
      // },
      // {
      //   "ifPlan": 1,
      //   "prizeKey": 580115847,
      //   "prizeType": 1,
      //   "dayLimitType": 1,
      //   "dayLimit": 1,
      //   "prizeImg": "//img10.360buyimg.com/imgzone/jfs/t1/215393/22/4731/33329/61946651E535ea01f/5cee5951d6bd1612.png",
      //   "couponBeginTime": null,
      //   "couponDiscount": 1,
      //   "couponEndTime": null,
      //   "couponId": 580115847,
      //   "couponQuota": 100,
      //   "couponType": 1,
      //   "couponValidateDay": 1,
      //   "createTime": 1761818442000,
      //   "discountType": 1,
      //   "endTime": 1763805621000,
      //   "expireType": 1,
      //   "numPerSending": 1,
      //   "planId": 580115847,
      //   "planName": "测试-sh101",
      //   "planStatus": 2,
      //   "putKey": null,
      //   "quantityFreeze": null,
      //   "quantityRemain": 10,
      //   "quantityTotal": 10,
      //   "quantityUsed": 0,
      //   "rangeType": 1,
      //   "shopId": 734259,
      //   "startTime": 1761818421000,
      //   "takeRule": 5,
      //   "version": 1,
      //   "prizeName": "测试-sh101",
      //   "unitPrice": 1,
      //   "sendTotalCount": 1,
      //   "unitCount": 1,
      //   "sortId": 3,
      //   "status": 1,
      //   "physicalType": 0
      // }
    ],
    sort: 1
  }
]);
const sectionInfo = ref([
  // {
  //   "title": "测试",
  //   "childSectionList": [
  //     {
  //       "childTitle": "范德萨"
  //     },
  //     {
  //       "childTitle": "发士大夫"
  //     },
  //     {
  //       "childTitle": "法撒旦撒"
  //     },
  //     {
  //       "childTitle": "大苏打撒旦撒"
  //     },
  //     {
  //       "childTitle": "大大苏打"
  //     },
  //     {
  //       "childTitle": "顶顶顶顶顶"
  //     }
  //   ]
  // },
  // {
  //   "title": "策哈哈哈哈哈哈",
  //   "childSectionList": [
  //     {
  //       "childTitle": "嗷嗷嗷"
  //     },
  //     {
  //       "childTitle": "对对都"
  //     },
  //     {
  //       "childTitle": "发发发"
  //     },
  //     {
  //       "childTitle": "热呃呃"
  //     }
  //   ]
  // },
  // {
  //   "title": "饿哇额",
  //   "childSectionList": [
  //     {
  //       "childTitle": ""
  //     }
  //   ]
  // }
]);
const officialTabs = ref([
  // {
  //   "officialTabText": "11111",
  //   "officialExampleList": [
  //     {
  //       "fileUrlList": [
  //         "//img10.360buyimg.com/imgzone/jfs/t1/224067/12/35156/30740/68fb26e7Ff81ffbf6/9ac3a7d1875871f4.png",
  //         "//img10.360buyimg.com/imgzone/jfs/t1/224067/12/35156/30740/68fb26e7Ff81ffbf6/9ac3a7d1875871f4.png"
  //       ],
  //       "titleText": "1",
  //       "contentText": "111"
  //     },
  //     {
  //       "fileUrlList": [
  //         "//img10.360buyimg.com/imgzone/jfs/t1/294839/31/24959/4646/68ff1606F188d3143/edf2edcafdc52623.png"
  //       ],
  //       "titleText": "2",
  //       "contentText": "222"
  //     },
  //     {
  //       "fileUrlList": [
  //         "//img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png"
  //       ],
  //       "titleText": "3",
  //       "contentText": "333"
  //     },
  //     {
  //       "fileUrlList": [
  //         "//img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png"
  //       ],
  //       "titleText": "4",
  //       "contentText": "444"
  //     }
  //   ]
  // },
  // {
  //   "officialTabText": "22222",
  //   "officialExampleList": [
  //     {
  //       "fileUrlList": [
  //         "//img10.360buyimg.com/imgzone/jfs/t1/294839/31/24959/4646/68ff1606F188d3143/edf2edcafdc52623.png"
  //       ],
  //       "titleText": "2",
  //       "contentText": "222"
  //     }
  //   ]
  // },
  // {
  //   "officialTabText": "33333",
  //   "officialExampleList": [
  //     {
  //       "fileUrlList": [
  //         "//img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png"
  //       ],
  //       "titleText": "3",
  //       "contentText": "333"
  //     }
  //   ]
  // }
]);
const rankPrizesList = ref([
  // {
  //   "rank": "1-7",
  //   "shopId": null,
  //   "sortId": 0,
  //   "status": 1,
  //   "skuCode": "s250926102131633033",
  //   "skuName": "集罐-sh13",
  //   "version": 1,
  //   "wmsCode": null,
  //   "dayLimit": 1,
  //   "prizeImg": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "prizeKey": "s250926102131633033",
  //   "prizeName": "集罐-sh13",
  //   "prizeType": 3,
  //   "unitCount": 1,
  //   "unitPrice": 1,
  //   "createTime": 1758853291000,
  //   "skuDetails": null,
  //   "activityIds": [],
  //   "dayLimitType": 1,
  //   "physicalType": 1,
  //   "quantityTotal": 10,
  //   "quantityFreeze": 0,
  //   "quantityRemain": 10,
  //   "sendTotalCount": 7,
  //   "skuMainPicture": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "quantityAvailable": 9,
  //   "quantityPreDeliver": 1
  // },
  // {
  //   "rank": "8-12",
  //   "shopId": null,
  //   "sortId": 1,
  //   "status": 1,
  //   "skuCode": "s250926093612510993",
  //   "skuName": "集罐-sh10",
  //   "version": 1,
  //   "wmsCode": null,
  //   "dayLimit": 1,
  //   "prizeImg": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "prizeKey": "s250926093612510993",
  //   "prizeName": "集罐-sh10",
  //   "prizeType": 3,
  //   "unitCount": 1,
  //   "unitPrice": 1,
  //   "createTime": 1758850572000,
  //   "skuDetails": null,
  //   "activityIds": [],
  //   "dayLimitType": 1,
  //   "physicalType": 0,
  //   "quantityTotal": 10,
  //   "quantityFreeze": 0,
  //   "quantityRemain": 10,
  //   "sendTotalCount": 5,
  //   "skuMainPicture": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "quantityAvailable": 7,
  //   "quantityPreDeliver": 3
  // },
  // {
  //   "rank": "13-14",
  //   "shopId": null,
  //   "sortId": 1,
  //   "status": 1,
  //   "skuCode": "s250926093612510993",
  //   "skuName": "集罐-sh10",
  //   "version": 1,
  //   "wmsCode": null,
  //   "dayLimit": 1,
  //   "prizeImg": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "prizeKey": "s250926093612510993",
  //   "prizeName": "集罐-sh10",
  //   "prizeType": 3,
  //   "unitCount": 1,
  //   "unitPrice": 1,
  //   "createTime": 1758850572000,
  //   "skuDetails": null,
  //   "activityIds": [],
  //   "dayLimitType": 1,
  //   "physicalType": 0,
  //   "quantityTotal": 10,
  //   "quantityFreeze": 0,
  //   "quantityRemain": 10,
  //   "sendTotalCount": 2,
  //   "skuMainPicture": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "quantityAvailable": 7,
  //   "quantityPreDeliver": 3
  // },
  // {
  //   "rank": "15-16",
  //   "shopId": null,
  //   "sortId": 1,
  //   "status": 1,
  //   "skuCode": "s250926093612510993",
  //   "skuName": "集罐-sh10",
  //   "version": 1,
  //   "wmsCode": null,
  //   "dayLimit": 1,
  //   "prizeImg": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "prizeKey": "s250926093612510993",
  //   "prizeName": "集罐-sh10",
  //   "prizeType": 3,
  //   "unitCount": 1,
  //   "unitPrice": 1,
  //   "createTime": 1758850572000,
  //   "skuDetails": null,
  //   "activityIds": [],
  //   "dayLimitType": 1,
  //   "physicalType": 0,
  //   "quantityTotal": 10,
  //   "quantityFreeze": 0,
  //   "quantityRemain": 10,
  //   "sendTotalCount": 2,
  //   "skuMainPicture": "https://img10.360buyimg.com/imgzone/jfs/t1/334491/38/16768/6827/68d38a9aF25f8e3f8/16ab2b7354c3e801.png",
  //   "quantityAvailable": 7,
  //   "quantityPreDeliver": 3
  // }
]);

const mineInfo = ref({
  nickName: '用户昵称',
  avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/357808/28/2652/14547/690849d1F77518e6a/81987f68d1a0e8a2.png',
  fansTotal: 1000,
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  console.log(data, '装修实时数据修改')
  officialTabs.value = data.officialTabs;
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  rankRuleText.value = data.rankRule;
  shopName.value = data.shopName;
  periodList.value = data.periodList;
  sectionInfo.value = data.sectionInfo;
  rankPrizesList.value = data.rankPrizesList;
  // taskList.value = data.taskList;
  console.log(data.taskList, 'activity')
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
// 任务监听
registerHandler('task', (data: any) => {
  console.log(data, 'task')
  if (data.change === true) {
    taskList.value = data.taskList;
    console.log(taskList.value, 'taskList')
    useSendMessage('task', 'taskList',data.taskList);
  }
});

registerHandler('activeKey', (data: any) => {
  if (data === '1' || data === '2') {
    switchPage(0);
  } else {
    switchPage(Number(data) - 2);
  }
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    rankRuleText.value = activityData.rankRule;
    periodList.value = activityData.periodList;
    sectionInfo.value = activityData.sectionInfo;
    rankPrizesList.value = activityData.rankPrizesList;
    taskList.value = activityData.taskList;
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    officialTabs.value = decoData.officialTabs;
    isLoadingFinish.value = true;
  }


});
</script>
<style scoped lang="scss">
.basePage{
  padding: 0 0 1.4rem 0;
  overflow: hidden;
}
.navBar{
  width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  z-index: 90; /* 确保导航栏在最上层 */

  .tabbarBox {
    background-size: 100%;
    background-repeat: no-repeat;
    height: 1.4rem;
    padding: 0.2rem 0 0 0;
    background-color: transparent;
    .uploadIcon{
      width: 1.1rem;
      height: 1.1rem;
      position: absolute;
      bottom: 1.1rem;
      z-index: 90;
      transform: translateX(-50%);
      left: 50%;
    }
  }
}
.van-tabbar-item--active{
  background-color: transparent;
}
</style>
