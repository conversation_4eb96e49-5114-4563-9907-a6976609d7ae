<template>
  <div class="container">
    <img class="rule" src="//img10.360buyimg.com/imgzone/jfs/t1/348376/38/4225/3058/68cef0baFa5a54892/b64c1bc86194942e.png" alt="" @click="showPopup.showRulePopup = true" />
    <img class="prize" src="//img10.360buyimg.com/imgzone/jfs/t1/347193/32/5821/3174/68cef0b1F7d1f9c0e/e37cc722eab4755e.png" alt="" @click="showPopup.showMyPrizePopup = true" />
    <div class="btn-group" v-if="pathParams.shop === 'so'">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/349755/9/12301/3736/68ecc96dF410c3ad6/7674f763719b8e4b.png" alt="" @click="goHref('https://item.jd.com/100205007955.html')" />
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/342457/13/10456/4216/68ecc96dFd8ec6f2d/ce1913e92086a583.png" alt="" @click="goHref('https://item.jd.com/100205007959.html')" />
    </div>
    <div class="btn-group" v-if="pathParams.shop === 'pop'">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/349755/9/12301/3736/68ecc96dF410c3ad6/7674f763719b8e4b.png" alt="" @click="goHref('https://item.jd.com/10181716483653.html')" />
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/342457/13/10456/4216/68ecc96dFd8ec6f2d/ce1913e92086a583.png" alt="" @click="goHref('https://item.jd.com/10181717400407.html')" />
    </div>
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide" v-for="(item, index) in swiperList" :key="index">
          <a :href="`#swiper-img-${index}`">
            <img class="swiper-img" :src="item.img" alt="" />
          </a>
        </div>
      </div>
    </div>
    <div class="calendar" id="swiper-img-4">
      <div class="calendar-header">
        <div class="month-display">
          <span style="font-size: 0.98rem">{{ currentMonth }}</span
          >月
        </div>
        <div class="month-selector">
          <img class="arrow" src="//img10.360buyimg.com/imgzone/jfs/t1/334114/20/11050/305/68bfbfb8Fbb276694/d34c3e3bc2846af4.png" alt="" @click="prevMonth" />
          <img class="arrow" src="//img10.360buyimg.com/imgzone/jfs/t1/323995/40/17637/335/68bed6e3F21222ed0/f650ab1a14f6ecba.png" alt="" @click="nextMonth" />
        </div>
        <div class="sign-info">
          <div class="sign-days">累计签到:&nbsp;&nbsp;{{ signData.signDays }} 天</div>
          <div class="sign-points">积分余额:&nbsp;&nbsp;{{ actData.pointTotal - actData.pointUsed }}积分</div>
        </div>
        <div class="sign-btn" @click="signIn()">{{ isTodaySigned ? '已签到' : '点击签到' }}</div>
      </div>
      <div class="calendar-body">
        <div class="days">
          <div
            v-for="day in calendarDays"
            :key="day.date"
            class="day"
            :class="{
              hidden: !day.inMonth,
              today: day.isToday,
              signed: day.isSigned,
              isSpecial: day.hasActivity || day.isSpecial,
            }"
            v-show="day.inMonth">
            <div class="day-number">{{ String(day.day).padStart(2, '0') }}</div>
            <!-- <div v-if="day.hasActivity" class="activity-tag">AAA活动</div> -->
            <div v-if="day.isSpecial" class="activity-tag">{{ dayjs(day.date).format('YYYY-MM-DD') === '2025-10-17' ? '新品开售' : '新品发布会' }}</div>
            <div v-if="day.hasActivity" class="activity-tag">晒照赢E卡</div>
          </div>
        </div>
      </div>
    </div>
    <img id="swiper-img-2" style="width: 6.6rem; margin: 0.4rem auto 0" src="//img10.360buyimg.com/imgzone/jfs/t1/348643/5/11952/16640/68ecfd03Fb10c0b91/3fec6fb94f17ca15.png" alt="" />
    <div class="pointsExchangeForGifts">
      <div class="pointsExchangeForGifts-info">
        <div class="info-title">老客感恩回馈月</div>
        <img class="info-img" src="//img10.360buyimg.com/imgzone/jfs/t1/324527/6/28083/19228/68e8a265Fc7cc73b5/86e1d74df9cb27ad.png" alt="" />
        <div class="info-title">可领5000积分</div>
        <div class="tips">老客下单X300系列即可领取<br /><span style="color: #6f6f6f">(如领取失败，可联系客服反馈)</span></div>
        <img class="btn" v-if="olderPointData?.status === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/337903/9/19154/2345/68e9f44dF4b6d6308/0375c672e26e7ad6.png" alt="" @click="gotoSkuPage(olderPointData?.skuId)" />
        <img class="btn" v-else :class="{ disabled: olderPointData?.status === 0 || olderPointData?.status === 3 }" src="//img10.360buyimg.com/imgzone/jfs/t1/344960/5/11306/2704/68e8a265F2e3d78bd/d1b1f1de50593e29.png" alt="" @click="exchangePoints('0', olderPointData?.status)" />
      </div>
      <div class="pointsExchangeForGifts-info">
        <div class="info-title" :class="{ disabled: dayjs().format('YYYY-MM-DD') < memberDay }">vivo影像会员日</div>
        <img class="info-img" :class="{ disabled: dayjs().format('YYYY-MM-DD') < memberDay, active: dayjs().format('YYYY-MM-DD') < memberDay }" style="width: 1.3rem" src="//img10.360buyimg.com/imgzone/jfs/t1/343656/26/11221/22707/68e8ccf6F61bd9e9e/2d8b5bd32c1a9014.png" alt="" />
        <div v-show="dayjs().format('YYYY-MM-DD') >= memberDay" class="info-title" :class="{ disabled: dayjs().format('YYYY-MM-DD') < memberDay }">可领5000积分</div>
        <div v-show="dayjs().format('YYYY-MM-DD') >= memberDay" class="tips" :class="{ disabled: dayjs().format('YYYY-MM-DD') < memberDay }">在会员日下单X300系列新品即可领取</div>
        <template v-if="dayjs().format('YYYY-MM-DD') >= memberDay">
          <img v-if="memberDayPointData?.status === 0" class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/337903/9/19154/2345/68e9f44dF4b6d6308/0375c672e26e7ad6.png" alt="" @click="gotoSkuPage(memberDayPointData?.skuId)" />
          <img v-else class="btn" :class="{ disabled: memberDayPointData?.status === 2 }" src="//img10.360buyimg.com/imgzone/jfs/t1/344960/5/11306/2704/68e8a265F2e3d78bd/d1b1f1de50593e29.png" alt="" @click="exchangePoints('1', memberDayPointData?.status)" />
        </template>
        <img v-else class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/344009/28/11528/3410/68e9b6b6F24b21b82/0061101f2ed9d1fc.png" alt="" @click="showToast('敬请期待')" />
      </div>
    </div>
    <img style="width: 6.6rem; margin: 0.5rem auto 0" id="swiper-img-1" src="//img10.360buyimg.com/imgzone/jfs/t1/298031/20/16512/17536/68ecfd03F20077627/18d8bdda645e7dbc.png" alt="" />
    <div class="exchange-content">
      <!-- 兑换区轮播：与顶部轮播结构一致 -->
      <div class="exchange-swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in pointPrizeList" :key="index">
            <img class="swiper-img" :src="item.prizeImg" alt="" />
            <div class="name">{{ item.prizeName }}</div>
            <div class="point">{{ item.point }}积分</div>
            <img class="btn disabled" v-if="item.status === 4" src="//img10.360buyimg.com/imgzone/jfs/t1/342381/18/12330/2181/68ece72aFfa061033/5446082c39dee902.png" alt="" />
            <img
              v-else
              class="btn"
              src="//img10.360buyimg.com/imgzone/jfs/t1/329641/7/21458/2573/68e9bf89F34cf36c5/b64ac8a122e1abb5.png"
              alt=""
              @click="
                prizeItem = item;
                showPopup.showConfirmPrizePopup = true;
              " />
          </div>
        </div>
      </div>
      <img class="img-more" src="//img10.360buyimg.com/imgzone/jfs/t1/333911/12/21205/1934/68e9c028F49186516/5a0cc321f274320f.png" alt="" />
    </div>
    <!-- <img style="width: 7rem; margin: 0.2rem auto 0" src="//img10.360buyimg.com/imgzone/jfs/t1/350639/1/2439/42458/68c3dd5fF95a4caa1/7dcbbc626ba39d52.png" alt="" /> -->
    <!-- 碎片碎片 -->
    <img class="puzzle-title1" id="swiper-img-0" src="//img10.360buyimg.com/imgzone/jfs/t1/324408/30/18480/5102/68c1460aFd5f900eb/29169b1cdddf89f3.png" alt="" />
    <img class="puzzle-title2" src="//img10.360buyimg.com/imgzone/jfs/t1/334812/1/11637/4034/68c1460aF8930e71b/18e06b1531c58720.png" alt="" />
    <div class="swiper-container-card">
      <div class="swiper-button-prev"></div>
      <div class="swiper-button-next"></div>
      <div class="swiper-wrapper">
        <div class="swiper-slide card" v-for="(item, index) in cardData" :key="index">
          <div class="progress">
            <img :src="progressImgList[item.cardInfos.filter((it:any) => it.number>0).length]" alt="" v-if="item.status !== 3" />
            <img :src="progressImgList[7]" alt="" v-else />
            <div class="draw-btn" v-if="item.cardInfos.filter((it:any) => it.number>0).length === 6 && item.status === 2" @click="drawPrize(item.groupId)"></div>
          </div>
          <div class="card-info">
            <div class="mask" v-if="item.groupId === 9999999999">敬请期待</div>
            <div class="card-item">
              <img v-for="(it, index) in item.cardInfos" :key="index" :class="`card-img-${index}`" :src="it.lightUp ? it.BCardImg : it.DCardImg" alt="" />
            </div>
          </div>
          <div class="smart-card-item">
            <div class="item" v-for="(it, index) in item.cardInfos" :key="index" :class="`smart-card-img-${index}`" @click="chooseCard(item, it)">
              <img :src="it.lightUp ? it.BCardImg : it.DCardImg" alt="" />
              <div class="card-num" v-if="it.number">{{ it.number }}</div>
            </div>
          </div>
          <div class="remain">抽碎片剩余次数：{{ actData.cardTotal - actData.cardUsed }}次</div>
          <div class="tips">5张重复碎片可兑换为任意一张碎片，点击碎片即可兑换！</div>
          <div class="puzzle-btn-group">
            <img src="//img10.360buyimg.com/imgzone/jfs/t1/344122/17/3746/4623/68c7fd21Fa1bb75de/e1316c887edd6a31.png" alt="" @click="item.groupId !== 9999999999 ? (showPopup.showCardRecordPopup = true) : showToast('敬请期待')" />
            <img src="//img10.360buyimg.com/imgzone/jfs/t1/343879/14/3698/3586/68c7fd20Fd8b677fc/9dc3eea56fab7893.png" alt="" @click="drawCards(item)" />
          </div>
        </div>
      </div>
    </div>
    <div class="prize-preview">
      <div class="prize-swiper-container swiper-container-prize">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in lotteryPrizeList" :key="index">
            <div class="prize-item">
              <div class="prize-img">
                <img :src="item.prizeImg || item.imgUrl" alt="" />
              </div>
              <div class="prize-name">{{ item.prizeName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 任务 -->
    <img class="task-title" src="//img10.360buyimg.com/imgzone/jfs/t1/343253/11/3700/5011/68c7fd20F98218c44/bf03501f743c07fd.png" alt="" />
    <div class="task-list">
      <div class="task-item" v-for="(item, index) in taskData" :key="index" :style="{ backgroundImage: `url(${item.decoration.imgUrl})` }">
        <div class="btn" @click="item.finished ? null : doTask(item)" :class="{ disabled: item.finished }">{{ item.finished ? '已完成' : item.decoration.btnText }}</div>
      </div>
    </div>
    <!-- 先人邀约 -->
    <!-- <img class="invite-img" id="swiper-img-3" src="//img10.360buyimg.com/imgzone/jfs/t1/244821/21/31202/118054/68ea150bF8d04fd96/f69c8303031f0491.png" alt="" @click="goHref('https://pro.m.jd.com/mall/active/oA8xRhwFHACd34ek7ekfbwEMRYd/index.html')" />
    <img class="invite-img" src="//img10.360buyimg.com/imgzone/jfs/t1/339889/30/18894/107225/68ea150bF51790d63/7bfd543b7555c583.png" alt="" @click="goHref('https://pro.m.jd.com/mall/active/3bZotQopHr3ozo6nXBkhUB28jRED/index.html')" /> -->
  </div>
  <RulePopup :showPopup="showPopup.showRulePopup" @close="showPopup.showRulePopup = false" :rule="rule.replaceAll('\n', '<br>')" />
  <MyPrizePopup :showPopup="showPopup.showMyPrizePopup" @close="showPopup.showMyPrizePopup = false" @showCardNum="showCardNum" @showAddress="showAddress" />
  <FailCardPopup :showPopup="showPopup.showFailCardPopup" @close="showPopup.showFailCardPopup = false" />
  <WinCardPopup :showPopup="showPopup.showWinCardPopup" :cardImg="cardImg" @close="showPopup.showWinCardPopup = false" />
  <ChooseCardPopup :showPopup="showPopup.showChooseCardPopup" @close="showPopup.showChooseCardPopup = false" :chooseCardId="chooseCardId" :chooseCardList="chooseCardList" @ExchangeConfirm="handleExchangeConfirm" />
  <ConfirmCardPopup :showPopup="showPopup.showConfirmCardPopup" @close="showPopup.showConfirmCardPopup = false" :chooseCardItem="chooseCardItem" @Exchange="handleExchange" />
  <ExchangeCardSuccess :showPopup="showPopup.showExchangeCardSuccess" @close="showPopup.showExchangeCardSuccess = false" :cardImg="cardImg" />
  <CardRecordPopup :showPopup="showPopup.showCardRecordPopup" @close="showPopup.showCardRecordPopup = false" />
  <FailPrizePopup :showPopup="showPopup.showFailPrizePopup" @close="showPopup.showFailPrizePopup = false" />
  <WinPrizePopup :showPopup="showPopup.showWinPrizePopup" :prizeInfo="prizeInfo" @close="showPopup.showWinPrizePopup = false" @fill="fillAddress" />
  <FillAddressPopup :showPopup="showPopup.showFillAddressPopup" @close="showPopup.showFillAddressPopup = false" :prizeInfo="prizeInfo" @refresh="refresh" />
  <addSkusPopup :showPopup="showPopup.showAddSkusPopup" @close="showPopup.showAddSkusPopup = false" @goAddTask="addSkuTask" />
  <ConfirmPrizePopup :showPopup="showPopup.showConfirmPrizePopup" @close="showPopup.showConfirmPrizePopup = false" :prizeItem="prizeItem" @Exchange="handleExchangeprize" />
  <ExchangePrizeSuccess :showPopup="showPopup.showExchangePrizeSuccess" @close="showPopup.showExchangePrizeSuccess = false" :prizeInfo="prizeInfo" @fill="fillAddress" />
  <ExchangePointsSuccess :showPopup="showPopup.showExchangePointsSuccess" @close="showPopup.showExchangePointsSuccess = false" />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, nextTick, onUnmounted, watch, inject } from 'vue';
import Swiper, { Autoplay, Navigation } from 'swiper';
import 'swiper/swiper.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import { showToast } from 'vant';
import { mainActivity, signDetail, sign, getRule, taskList, draw, appointSku, olderPoint, memberDayPoint, memberDayPointDraw, browseSearchPage, pointExchange, pointPrize, olderPointDraw, browseSku, browseArea, lotteryPrize, cardList, lotteryDrawCard, exchangeCard, addSku } from './ajax/ajax';
import { MainData, SignData, TaskItem } from './ajax/type';
import dayjs from 'dayjs';
import { addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';

import RulePopup from './compoents/RulePopup.vue';
import MyPrizePopup from './compoents/MyPrizePopup.vue';
import FailCardPopup from './compoents/FailCardPopup.vue';
import WinCardPopup from './compoents/WinCardPopup.vue';
import ChooseCardPopup from './compoents/ChooseCardPopup.vue';
import ConfirmCardPopup from './compoents/ConfirmCardPopup.vue';
import ExchangeCardSuccess from './compoents/ExchangeCardSuccess.vue';
import CardRecordPopup from './compoents/CardRecordPopup.vue';
import FailPrizePopup from './compoents/FailPrizePopup.vue';
import WinPrizePopup from './compoents/WinPrizePopup.vue';
import FillAddressPopup from './compoents/FillAddressPopup.vue';
import addSkusPopup from './compoents/addSkusPopup.vue';
import ConfirmPrizePopup from './compoents/ConfirmPrizePopup.vue';
import ExchangePrizeSuccess from './compoents/ExchangePrizeSuccess.vue';
import ExchangePointsSuccess from './compoents/ExchangePointsSuccess.vue';

const pathParams = inject('pathParams') as any;
// 将时间戳转换为日期字符串格式 YYYY-MM-DD
const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const swiperList = [
  {
    img: '//img10.360buyimg.com/imgzone/jfs/t1/328914/40/18194/25410/68bff193F2b8b1ea7/35e0a8d2ebfb1093.png',
  },
  {
    img: '//img10.360buyimg.com/imgzone/jfs/t1/246892/6/32810/29129/68ecac1eF101d13be/c1e371be28511606.png',
  },
  {
    img: '//img10.360buyimg.com/imgzone/jfs/t1/338702/13/19790/25218/68ecac1eFdb8c0898/f5bc5af02fe9cc06.png',
  },
  {
    img: '//img10.360buyimg.com/imgzone/jfs/t1/339786/15/8801/24013/68bff3b6F23b249d5/22d472e6e7dfc68b.png',
  },
];
// 活动数据
const actData = ref<MainData>({
  cardTotal: 0,
  cardUsed: 0,
  total: 0,
  used: 0,
  pointTotal: 0,
  pointUsed: 0,
  popMember: false,
});
const olderPointData = ref();
const memberDayPointData = ref();
const memberDay = ref('2025-10-31');
const signData = ref<SignData>({
  sign: false,
  signDays: 0,
  signRecordList: [],
});
const showPopup = reactive({
  showRulePopup: false,
  showMyPrizePopup: false,
  showFailCardPopup: false,
  showWinCardPopup: false,
  showChooseCardPopup: false,
  showConfirmCardPopup: false,
  showExchangeCardSuccess: false,
  showCardRecordPopup: false,
  showFailPrizePopup: false,
  showWinPrizePopup: false,
  showFillAddressPopup: false,
  showAddSkusPopup: false,
  showConfirmPrizePopup: false,
  showExchangePrizeSuccess: false,
  showExchangePointsSuccess: false,
});
const taskData = ref<TaskItem[]>([]);
const addSkuIDs = ref<string[]>([]);
const rule = ref('');
const cardData = ref<any[]>([]);
const chooseCardList = ref<any[]>([]);
const chooseCardId = ref();
const chooseCardItem = ref<any>({});
const cardImg = ref('');
const prizeInfo = ref();
const lotteryPrizeList = ref<any[]>([]);
const pointPrizeList = ref<any[]>([]);
const prizeItem = ref<any>({});
// 日历相关数据
const currentDate = new Date();
const currentMonth = ref(currentDate.getMonth() + 1); // 当前月份，从0开始，所以+1
const currentYear = ref(currentDate.getFullYear()); // 当前年份
// 进度条配置
const progressImgList = [
  '//img10.360buyimg.com/imgzone/jfs/t1/333061/20/12055/9537/68c27185Fb2db1781/f52c4de2f7e41f01.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/341663/16/2124/10737/68c26405Fcb75fca7/e6c281f470ac6388.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/325160/4/18733/11616/68c26404Febf4a852/f89b48c60a121776.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/336830/3/9256/12457/68c26404F5376d93d/0c99d6cf1634905f.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/339810/1/7964/13364/68c26404Faf6d1180/d635bd5123b3e51c.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/333970/24/11923/13946/68c26404F7170f9fa/58e9885147850d99.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/347471/25/3981/19772/68c911f5F2f6565c9/35d2f74a25873f15.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/337267/12/11540/6450/68c911f5Ff46666ea/7e0e4c813954d5f9.png',
];
// 特殊日期配置
const activityDates = ['2025-10-14', '2025-10-21', '2025-10-28'];
const specialDates = ['2025-10-13', '2025-10-17'];

// 计算日历天数
const calendarDays = ref<any[]>([]);
// 判断今日是否已签到
const isTodaySigned = ref(false);

// 生成日历数据
const generateCalendar = () => {
  const days = [];
  const lastDay = new Date(currentYear.value, currentMonth.value, 0);
  // 只填充当月的日期
  let startDay = 1;
  if (currentMonth.value === 9) {
    startDay = 8; // 九月从8号开始展示
  }
  for (let i = startDay; i <= lastDay.getDate(); i++) {
    const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
    const today = new Date();
    const isToday = today.getDate() === i && today.getMonth() === currentMonth.value - 1 && today.getFullYear() === currentYear.value;
    days.push({
      day: i,
      date: dateStr,
      inMonth: true,
      isToday,
      isSigned: signData.value.signRecordList.some((record) => {
        const recordDate = formatTimestamp(Number(record));
        return recordDate === dateStr;
      }),
      hasActivity: activityDates.includes(dateStr),
      isSpecial: specialDates.includes(dateStr),
    });
  }
  calendarDays.value = days;

  // 更新今日是否已签到状态
  const today = new Date();
  const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  isTodaySigned.value = signData.value.signRecordList.some((record) => {
    const recordDate = formatTimestamp(Number(record));
    return recordDate === todayStr;
  });
};

// 切换到上个月
const prevMonth = () => {
  if (currentMonth.value === 9) {
    currentMonth.value = 10;
  } else {
    currentMonth.value = 9;
  }
  generateCalendar();
};

// 切换到下个月
const nextMonth = () => {
  if (currentMonth.value === 10) {
    currentMonth.value = 9;
  } else {
    currentMonth.value = 10;
  }
  generateCalendar();
};
// 活动信息
const activityInfo = async () => {
  actData.value = await mainActivity();
};
// 签到详情
const getSignDetail = async () => {
  signData.value = await signDetail();
};
// 签到功能
const signIn = async () => {
  try {
    const res = await sign();
    if (res && res.code === 200) {
      // 重新获取签到详情
      await activityInfo();
      // 确保获取到签到详情后再更新日历显示
      taskData.value = await taskList();
      await getSignDetail().then(() => {
        showToast('恭喜您，签到成功');
        generateCalendar();
      });
    }
  } catch (error) {
    showToast('签到失败，请稍后重试');
  }
};
// 获取卡片列表
const getCardList = async () => {
  cardData.value = await cardList();
  // 一共5套碎片，未开启的碎片套组展示敬请期待，接口只返回已开启的套组
  // 添加四条相同的死数据
  for (let i = 0; i < 5 - cardData.value.length; i++) {
    cardData.value.push({
      cardInfos: [
        {
          BCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/344428/28/3396/18714/68c76e4bFf4b403b7/3567398dc3ad4c50.png',
          DCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/342864/36/2036/6578/68c23e12F8a67bcfb/d9ed9c7a15b3e922.png',
          cardContent: '碎片1',
          cardId: 7,
          cardName: '碎片1',
          lightUp: false,
          number: 0,
          sortId: 1,
        },
        {
          BCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/328934/38/20131/10054/68c76e4bFd3f299b7/ad8e01dabc45dde6.png',
          DCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/346323/33/1973/6549/68c23e11F8db4764f/1772b483bf959481.png',
          cardContent: '碎片2',
          cardId: 8,
          cardName: '碎片2',
          lightUp: false,
          number: 0,
          sortId: 2,
        },
        {
          BCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/336576/34/10919/29087/68c76e4aFec48ab8a/f5e4b93d8735c77e.png',
          DCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/348627/32/2045/8526/68c23e11F08a704b4/e6ddebe0f55e5971.png',
          cardContent: '碎片3',
          cardId: 9,
          cardName: '碎片3',
          lightUp: false,
          number: 0,
          sortId: 3,
        },
        {
          BCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/332731/22/13434/27836/68c76e4aF8fe9e84e/c5000ca355a8b69d.png',
          DCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/350994/23/1985/8952/68c23e11Fdd8fe32a/aeb1f4e99cb35e42.png',
          cardContent: '碎片4',
          cardId: 10,
          cardName: '碎片4',
          lightUp: false,
          number: 0,
          sortId: 4,
        },
        {
          BCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/346374/11/3590/23872/68c76e49F4ee5e813/aa2212138a22f59e.png',
          DCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/325656/5/18422/6539/68c23e11F14be101a/2efe430181f35cfe.png',
          cardContent: '碎片5',
          cardId: 11,
          cardName: '碎片5',
          lightUp: false,
          number: 0,
          sortId: 5,
        },
        {
          BCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/294017/14/24563/19524/68c76e49Fb4fc1cb3/0d1189be1ea273d5.png',
          DCardImg: '//img10.360buyimg.com/imgzone/jfs/t1/327542/31/18748/6672/68c23e10Fe56a34ad/47a9aabd7466a5ba.png',
          cardContent: '碎片6',
          cardId: 12,
          cardName: '碎片6',
          lightUp: false,
          number: 0,
          sortId: 6,
        },
      ],
      groupId: 9999999999,
      status: 0,
    });
  }
};
// 加购商品
const addSkuTask = async () => {
  addSkuToCart(addSkuIDs.value);
  const res = await addSku();
  if (res && res === 200) {
    showToast('加购成功');
    // 刷新主接口和任务接口
    setTimeout(async () => {
      await activityInfo();
      taskData.value = await taskList();
    }, 1000);
  }
};
// 做任务
const doTask = async (item: TaskItem) => {
  switch (item.taskType) {
    case 6:
      const res = await appointSku();
      if (res && res === 200) {
        showToast('预约成功');
        setTimeout(async () => {
          goHref(item.linkUrl);
          // 刷新主接口和任务接口
          await activityInfo();
          taskData.value = await taskList();
        }, 1000);
      }
      break;
    case 3:
      window.localStorage.setItem('browseTimestamp', dayjs().valueOf().toString());
      window.localStorage.setItem('taskType', '3');
      goHref(item.linkUrl);
      break;
    case 14:
      document.getElementById('swiper-img-0')?.scrollIntoView({ behavior: 'smooth' });
      break;
    case 13:
    case 26:
      const openCardLink = `${item.linkUrl}&returnUrl=${encodeURIComponent(`${window.location.href}`)}`;
      window.localStorage.setItem('browseTimestamp', dayjs().valueOf().toString());
      window.localStorage.setItem('taskType', 'openCard');
      goHref(openCardLink);
      break;
    case 7:
      showPopup.showAddSkusPopup = true;
      if (item.popSkuIds && typeof item.popSkuIds === 'string') {
        const popSkuIdArray = item.popSkuIds.split(',');
        popSkuIdArray.forEach((skuId) => {
          addSkuIDs.value.push(skuId);
        });
      }
      if (item.skuIds && typeof item.skuIds === 'string') {
        const skuIdArray = item.skuIds.split(',');
        skuIdArray.forEach((skuId) => {
          addSkuIDs.value.push(skuId);
        });
      }
      break;
    case 4:
      window.localStorage.setItem('browseTimestamp', dayjs().valueOf().toString());
      window.localStorage.setItem('taskType', '4');
      goHref(item.linkUrl);
      break;
    case 23:
      goHref(item.linkUrl);
      break;
    case 11:
      window.localStorage.setItem('browseTimestamp', dayjs().valueOf().toString());
      window.localStorage.setItem('taskType', '11');
      goHref(item.linkUrl);
      break;
  }
};
// 抽取卡片
const drawCards = async (item: any) => {
  console.log(item);
  if (item.groupId === 9999999999) {
    showToast('敬请期待');
    return;
  }
  if (actData.value.cardTotal - actData.value.cardUsed <= 0) {
    showToast('抽碎片机会不足');
    return false;
  }
  if (item.cardInfos.filter((it: any) => it.number > 0).length === 6 && item.groupId === 4) {
    showToast('下张碎片即将开启，敬请期待~');
    return;
  }
  if (item.cardInfos.filter((it: any) => it.number > 0).length === 6 && item.groupId !== 4) {
    console.log(item.cardInfos.filter((it: any) => it.number > 0).length);
    showToast('该拼图已集齐，快去参与其他拼图抽奖吧~');
    return;
  }
  if (item.status === 0) {
    showToast('抱歉，您未解锁该拼图');
    return;
  }
  const res = await lotteryDrawCard(item.groupId);
  if (res.status === 1) {
    await activityInfo();
    await getCardList();
    cardImg.value = res.cardImg;
    showPopup.showWinCardPopup = true;
  } else {
    showPopup.showFailCardPopup = true;
  }
};
// 选择卡片
const chooseCard = (item: any, it: any) => {
  if (item.groupId === 9999999999) {
    showToast('敬请期待');
    return;
  }
  if (it.number < 6) {
    showToast('抱歉,你目前重复碎片数不足6个');
    return;
  }
  if (item.cardInfos.filter((it: any) => it.number > 0).length >= 6) {
    showToast('下张碎片即将开启，敬请期待~');
    return;
  }
  chooseCardList.value = item;
  chooseCardId.value = it.cardId;
  showPopup.showChooseCardPopup = true;
};

// 处理从ChooseCardPopup组件传递的cardId
const handleExchangeConfirm = (selectedCard: any) => {
  // 关闭弹窗
  chooseCardItem.value = selectedCard;
  showPopup.showChooseCardPopup = false;
  showPopup.showConfirmCardPopup = true;
};

// 兑换卡片
const handleExchange = async (chooseCardItem: any) => {
  const res = await exchangeCard(chooseCardId.value, chooseCardItem.cardId);
  if (res === 200) {
    showPopup.showConfirmCardPopup = false;
    showPopup.showExchangeCardSuccess = true;
    cardImg.value = chooseCardItem.BCardImg;
    await getCardList();
  }
};
// 兑换奖品
const handleExchangeprize = async (prizeItem: any) => {
  const res = await pointExchange(prizeItem.prizeId);
  console.log('🚀 ~ handleExchangeprize ~ res:', res);
  if (res.code === 200) {
    prizeInfo.value = res.data;
    showPopup.showConfirmPrizePopup = false;
    showPopup.showExchangePrizeSuccess = true;
    await activityInfo();
    pointPrizeList.value = await pointPrize();
  }
};
// 抽奖
const drawPrize = async (groupId: any) => {
  const res = await draw(groupId);
  if (res.status === 1) {
    showPopup.showWinPrizePopup = true;
    prizeInfo.value = res;
  } else if (res.status === 0) {
    showPopup.showFailPrizePopup = true;
  }
  refresh();
};
// 填写地址
const fillAddress = () => {
  showPopup.showWinPrizePopup = false;
  showPopup.showExchangePrizeSuccess = false;
  showPopup.showFillAddressPopup = true;
};
// 填写地址后刷新
const refresh = async () => {
  await activityInfo();
  await getCardList();
};
// 打开卡密弹窗
const showCardNum = (item: any) => {
  showPopup.showMyPrizePopup = false;
  showPopup.showWinPrizePopup = true;
  prizeInfo.value = item;
};
// 打开填写地址弹窗
const showAddress = (item: any) => {
  showPopup.showMyPrizePopup = false;
  showPopup.showFillAddressPopup = true;
  prizeInfo.value = item;
};
// 兑换积分
const exchangePoints = async (type: string, status: number) => {
  if (type === '0') {
    if (status === 0) {
      showToast('抱歉，您不符合店铺老客身份');
      return;
    }
    if (status === 3) {
      showToast('抱歉，您已领取过该积分');
      return;
    }
    const res = await olderPointDraw();
    if (res.code === 200) {
      showPopup.showExchangePointsSuccess = true;
      await activityInfo();
      olderPointData.value = await olderPoint();
    }
  }
  if (type === '1') {
    if (status === 2) {
      showToast('抱歉，您已领取过该积分');
      return;
    }
    const res = await memberDayPointDraw();
    if (res.code === 200) {
      showPopup.showExchangePointsSuccess = true;
      await activityInfo();
      memberDayPointData.value = await memberDayPoint();
    }
  }
};
Swiper.use([Autoplay, Navigation]);
let mySwiper: Swiper;
let mySwiperCard: Swiper;
const initSwiper = () => {
  mySwiper = new Swiper('.swiper-container', {
    // autoplay: true,
    slidesPerView: 2.5,
    spaceBetween: 5,
  });

  // 初始化兑换区轮播
  new Swiper('.exchange-swiper-container', {
    slidesPerView: 2.8,
    spaceBetween: 10,
    observer: true,
    observeParents: true,
    observeSlideChildren: true,
  });

  // 初始化奖品轮播
  new Swiper('.swiper-container-prize', {
    slidesPerView: 5,
    spaceBetween: 10,
    loop: true,
    observer: true,
    observeParents: true,
    observeSlideChildren: true,
    autoplay: {
      delay: 1000,
      disableOnInteraction: false,
    },
  });
};
const initSwiperCard = () => {
  mySwiperCard = new Swiper('.swiper-container-card', {
    slidesPerView: 1,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  });
};
const goHref = (url: string) => {
  localStorage.setItem('browseStartTime', String(Date.now()));
  window.location.href = url;
};
const handleVisiable = async () => {
  if (document.visibilityState === 'visible') {
    // 当页面重新可见时
    const timestamp = window.localStorage.getItem('browseTimestamp');
    const taskType = window.localStorage.getItem('taskType');
    const now = dayjs().valueOf();
    if (taskType === 'openCard') {
      await activityInfo();
      taskData.value = await taskList();
      window.localStorage.removeItem('taskType');
      window.localStorage.removeItem('browseTimestamp');
      return;
    }
    if (timestamp) {
      if (timestamp && now - parseInt(timestamp) > 30000) {
        // 超过30秒，调用接口
        let res;
        if (taskType === '3') {
          res = await browseSku();
        } else if (taskType === '4') {
          res = await browseArea();
        } else if (taskType === '11') {
          res = await browseSearchPage();
        }
        if (res && res === 200) {
          await activityInfo();
          taskData.value = await taskList();
          window.localStorage.removeItem('taskType');
          window.localStorage.removeItem('browseTimestamp');
        }
      } else {
        if (taskType === '3') {
          showToast('浏览商品时间小于30s');
        } else if (taskType === '4') {
          showToast('浏览会场时间小于30s');
        } else if (taskType === '11') {
          showToast('浏览新品搜索页时间小于30s');
        }
      }
      // 清除缓存，避免重复触发
      window.localStorage.removeItem('taskType');
      window.localStorage.removeItem('browseTimestamp');
    }
  }
};
onMounted(async () => {
  document.addEventListener('visibilitychange', handleVisiable);
  lotteryPrizeList.value = await lotteryPrize();
  await getCardList();
  nextTick(() => {
    initSwiper();
    initSwiperCard();
  });
  if (currentMonth.value !== 9 && currentMonth.value !== 10) {
    currentMonth.value = 9; // 如果当前不是9月或10月，默认显示9月
  }
  await activityInfo();
  await getSignDetail().then(() => {
    // 初始化日历，默认显示当前月份
    generateCalendar();
  });
  pointPrizeList.value = await pointPrize();
  olderPointData.value = await olderPoint();
  memberDayPointData.value = await memberDayPoint();
  rule.value = await getRule();
  taskData.value = await taskList();
});
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisiable);
});
</script>
<style scoped lang="scss">
.container {
  width: 7.5rem;
  min-height: 100vh;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/331361/30/22456/60966/68ecfd71F46468dbe/715c3c02156a4189.jpg') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 13rem;
  padding-bottom: 0.5rem;
  .rule {
    width: 1.33rem;
    position: absolute;
    top: 0.72rem;
    right: 0;
  }
  .prize {
    width: 1.33rem;
    position: absolute;
    top: 1.5rem;
    right: 0;
  }
  .btn-group {
    width: 100%;
    display: flex;
    justify-content: space-around;
    position: absolute;
    top: 4.5rem;
    padding: 0 0.5rem;
    img {
      width: 2.7rem;
    }
  }
  .swiper-container {
    width: 7rem;
    margin: 0 auto;
    overflow: hidden;

    .swiper-wrapper {
      width: 7rem;
      .swiper-slide {
        width: 3.15rem;
        .swiper-img {
          width: 100%;
        }
      }
    }
  }
  .calendar {
    width: 7rem;
    height: 6rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/329252/33/21549/39271/68ecfc8eF1b3e799b/0efe86a14a1a73e6.png') no-repeat;
    background-size: 100% 100%;
    margin: 0.4rem auto 0;
    padding: 0.3rem 0.2rem;
    border-radius: 0.2rem;
    box-sizing: border-box;
    overflow: hidden;

    .calendar-header {
      height: 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.1rem 0 0.1rem 0.2rem;
      .month-display {
        width: 1.5rem;
        font-size: 0.32rem;
        font-weight: bold;
        color: #6ac5ff;
        margin: 0.05rem 0;
        text-align: center;
      }
      .month-selector {
        display: flex;
        height: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        width: 0.8rem;

        .arrow {
          width: 0.4rem;
          cursor: pointer;
        }
      }

      .sign-info {
        flex: 1;
        text-align: left;
        padding-left: 0.2rem;

        .sign-days,
        .sign-points {
          font-size: 0.22rem;
          color: #666;
          margin: 0.03rem 0;
        }
      }

      .sign-btn {
        width: 1.8rem;
        height: 0.7rem;
        line-height: 0.7rem;
        text-align: center;
        background: linear-gradient(to right, #4bb5fd, #6fcbff);
        color: white;
        border-radius: 0.35rem;
        font-size: 0.26rem;
        cursor: pointer;
      }

      .sign-btn.signed {
        background: linear-gradient(90deg, #cccccc, #999999);
        cursor: not-allowed;
      }
    }

    .calendar-body {
      margin-top: 0.2rem;
      height: 2.5rem;
      overflow: auto;

      .days {
        display: flex;
        flex-wrap: wrap;

        .day {
          width: 0.88rem;
          height: 1.21rem;
          margin-left: 0.04rem;
          margin-bottom: 0.04rem;
          background-color: #ffffff;
          border-radius: 0.1rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: relative;

          .day-number {
            font-size: 0.34rem;
            color: #333;
          }

          &.hidden {
            display: none;
          }

          &.today {
            border: solid 0.04rem #4460dc;
          }

          &.signed {
            background-color: #91cbf6;
            border: none;
            .day-number {
              color: #fff;
              margin-top: -0.5rem;
            }
          }

          .activity-tag,
          .special-tag {
            font-size: 0.16rem;
            padding: 0.02rem 0.05rem;
            border-radius: 0.05rem;
            margin-top: 0.4rem;
            white-space: nowrap;
            transform: scale(0.8);
          }

          .activity-tag {
            background-color: #ff6b6b;
            color: white;
          }

          .special-tag {
            background: linear-gradient(to right, #ff6b6b, #ffb199);
            color: white;
          }

          &.isSpecial {
            background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/330737/24/15981/6524/68cf7a25F8971e41f/9ce5672ebd3f00fc.png');
            background-size: cover;
            background-position: center;

            .day-number {
              color: #fff;
              font-weight: bold;
            }
          }
        }
      }
    }

    .calendar-footer {
      margin-top: 0.1rem;
      padding: 0.1rem 0;

      .sign-desc {
        font-size: 0.22rem;
        color: #666;
        line-height: 1.2;
      }
    }
  }
  .pointsExchangeForGifts {
    width: 7rem;
    margin: 0.2rem auto 0;
    display: flex;
    justify-content: space-between;
    .pointsExchangeForGifts-info {
      width: 3.4rem;
      height: 4.2rem;
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/245604/38/31323/8742/68e8a265F76d25764/4aaeed8a8cf91d84.png') no-repeat;
      background-size: 100%;
      padding-top: 0.1rem;
      display: flex;
      align-items: center;
      flex-direction: column;
      .info-title {
        font-size: 0.31rem;
        color: #000000;
        margin-top: 0.2rem;
      }
      .info-img {
        width: 1.2rem;
        margin-top: 0.2rem;
      }
      .active {
        width: 2rem !important;
        margin-bottom: 0.61rem;
      }
      .tips {
        width: 100%;
        padding: 0 0.5rem;
        font-size: 0.16rem;
        color: #000000;
        text-align: center;
        margin-top: 0.2rem;
      }
      .btn {
        width: 1.6rem;
        margin-top: 0.1rem;
      }
      .disabled {
        filter: grayscale(1);
        color: #999999;
      }
    }
  }
  .exchange-content {
    width: 7rem;
    height: 4.7rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/339758/3/18536/9898/68e8bfc0F5a7cd797/96050e53920327e4.png') no-repeat;
    background-size: 100%;
    margin: 0.2rem auto 0;
    padding-top: 0.4rem;
    .exchange-swiper-container {
      width: 90%;
      overflow: hidden;
      margin: 0 auto;
      .swiper-slide {
        width: 2.27rem;
        height: 3.6rem;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/338474/3/17996/2969/68e8bfc0F97a030f6/b0c7cd32d0c2173c.png') no-repeat;
        background-size: 100%;
        padding-top: 0.15rem;
        .swiper-img {
          width: 1.9rem;
          margin: 0 auto;
          border-radius: 0.1rem;
        }
        .name {
          width: 100%;
          margin-top: 0.1rem;
          padding: 0 0.2rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 0.2rem;
          color: #000000;
          text-align: center;
        }
        .point {
          width: 100%;
          font-size: 0.24rem;
          text-align: center;
          margin-top: 0.1rem;
        }
        .btn {
          width: 1.2rem;
          margin: 0.1rem auto 0;
        }
      }
    }
    .img-more {
      width: 1.5rem;
      margin: 0.2rem auto 0;
    }
  }
  .puzzle-title1 {
    width: 4.9rem;
    margin: 0.5rem auto 0.3rem;
  }
  .puzzle-title2 {
    width: 5.3rem;
    margin: 0 auto 0.2rem;
  }

  .swiper-container-card {
    /* width: 4.8rem;
    height: 7rem; */
    margin: 0.2rem auto 0;
    overflow: hidden;
    position: relative;

    .swiper-button-prev,
    .swiper-button-next {
      width: 0.5rem;
      height: 0.75rem;
      background-size: 100%;
      position: absolute;
    }

    .swiper-button-prev {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/336137/39/11418/5120/68c91221Feb19d941/47c5cc02fca59cda.png');
      left: 0.5rem;
      top: 4.2rem;
    }

    .swiper-button-next {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/332914/40/13956/4990/68c91221F0b17fadb/19dce809a3adfea1.png');
      right: 0.5rem;
      top: 4.2rem;
    }

    .swiper-button-prev::after,
    .swiper-button-next::after {
      display: none;
    }

    .card {
      // width: 7rem;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      .progress {
        width: 5.8rem;
        position: relative;
        margin: 0 auto;

        img {
          width: 100%;
        }

        .draw-btn {
          width: 0.5rem;
          height: 0.5rem;
          position: absolute;
          top: 0.2rem;
          right: 0.5rem;
        }
      }

      .card-info {
        margin: 0 auto;
        width: 4.8rem;
        height: 7rem;
        padding: 0.15rem;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/348995/1/4035/8140/68c8ff0aF54443f4d/635ec6630a4513a2.png') no-repeat;
        background-size: 100%;
        position: relative;
        .mask {
          width: 4.8rem;
          height: 7rem;
          position: absolute;
          top: 0;
          left: 0;
          padding: 0.15rem;
          z-index: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 0.2rem;
        }
        .card-item {
          position: relative;

          .card-img-0 {
            position: absolute;
            width: 2.3rem;
            /* height: 2.95rem; */
            top: 0;
            left: 0;
          }

          .card-img-1 {
            position: absolute;
            width: 2.9rem;
            top: 0;
            right: 0;
          }

          .card-img-2 {
            position: absolute;
            width: 2.95rem;
            top: 2.2rem;
            left: 0;
          }

          .card-img-3 {
            position: absolute;
            width: 2.285rem;
            top: 1.57rem;
            right: 0;
          }

          .card-img-4 {
            position: absolute;
            width: 2.28rem;
            top: 3.8rem;
            left: 0;
          }

          .card-img-5 {
            position: absolute;
            width: 2.92rem;
            top: 4.42rem;
            right: 0;
          }
        }
      }

      .smart-card-item {
        width: 7rem;
        height: 1.4rem;
        margin: 0.2rem auto 0;
        display: flex;
        justify-content: space-around;

        .item {
          position: relative;

          .card-num {
            width: 0.3rem;
            height: 0.3rem;
            background-color: #5ca4f5;
            position: absolute;
            top: -0.1rem;
            right: -0.1rem;
            border-radius: 0.3rem;
            text-align: center;
            line-height: 0.3rem;
            font-size: 0.24rem;
            color: #fff;
          }

          img {
            width: 100%;
            object-fit: contain;
          }
        }

        .smart-card-img-2 {
          .card-num {
            right: 0.1rem;
          }
        }

        .smart-card-img-3,
        .smart-card-img-4 {
          .card-num {
            top: 0.15rem;
          }
        }

        .smart-card-img-0,
        .smart-card-img-1,
        .smart-card-img-2,
        .smart-card-img-5 {
          margin-top: 0.25rem;
        }

        .smart-card-img-0,
        .smart-card-img-3,
        .smart-card-img-4 {
          width: 0.88rem;
        }

        .smart-card-img-1,
        .smart-card-img-2,
        .smart-card-img-5 {
          width: 1.12rem;
        }
      }
    }

    .remain {
      width: 100%;
      text-align: center;
      font-size: 0.24rem;
      color: #000000;
      margin-top: 0.1rem;
    }

    .tips {
      width: 100%;
      text-align: center;
      font-size: 0.22rem;
      color: #000000;
      margin-top: 0.1rem;
    }

    .puzzle-btn-group {
      width: 7rem;
      margin: 0.3rem auto 0;
      display: flex;
      justify-content: space-around;

      img {
        width: 3rem;
      }
    }
  }

  .prize-preview {
    width: 7rem;
    height: 3rem;
    margin: 0.5rem auto 0;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/328578/21/19272/16094/68c3eef0F3d33f48c/20a541eb206fe92e.png') no-repeat;
    background-size: 100%;
    padding: 0.6rem 0.2rem;

    .prize-title {
      width: 100%;
      height: auto;
      display: block;
    }

    .prize-swiper-container {
      margin: 0.25rem auto;
      position: relative;
      overflow: hidden;

      .swiper-slide {
        display: flex;
        justify-content: center;
      }

      .prize-item {
        width: 1.2rem;
        display: flex;
        flex-direction: column;
        align-items: center;

        .prize-img {
          width: 1.2rem;
          height: 1.2rem;
          border-radius: 0.1rem;
          overflow: hidden;
          margin-bottom: 0.1rem;
          background-color: #fff;
          border-radius: 1.2rem;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .prize-name {
          width: 100%;
          font-size: 0.24rem;
          color: #333;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .task-title {
    width: 4.47rem;
    margin: 0.2rem auto 0.3rem;
  }
  .task-list {
    width: 7.2rem;
    margin: 0 auto;
    .task-item {
      width: 100%;
      height: 1.5rem;
      background-repeat: no-repeat;
      background-size: 100%;
      position: relative;
      .btn {
        width: 1.34rem;
        height: 0.57rem;
        background-color: #333333;
        border-radius: 0.28rem;
        border: solid 0.02rem #ffffff;
        font-size: 0.26rem;
        color: #fffafa;
        position: absolute;
        top: 0.45rem;
        right: 0.5rem;
        text-align: center;
        line-height: 0.55rem;
      }
    }
  }
  .invite-img {
    width: 7.2rem;
    margin: 0.5rem auto 0;
  }
}
</style>
<style>
::-webkit-scrollbar {
  display: none;
}
* {
  box-sizing: border-box;
}
html {
  background-color: #cfe5f8;
}
img {
  object-fit: contain;
}
.disabled {
  filter: grayscale(1);
}
</style>
