<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv">
      <img :src="furnish.actBg ??
        '//img10.360buyimg.com/imgzone/jfs/t1/244592/32/36444/200285/68ff3047F9ca1750d/adcb45bba488fc0f.png'
        " alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name"></div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtnRules.value" @click="showRulePop" />
        </div>
      </div>
    </div>
    <div class="stepDiv" :style="{ backgroundImage: `url(${furnish.stepBg})` }"></div>
    <div class="seriesSkuBgAll" :style="{ backgroundImage: `url(${furnish.seriesSkuBg})` }">
      <div class="seriesSkuListBgDiv">
        <div class="seriesSkuItemBgDiv" v-for="(item, index) in seriesSkuFinalResult" :key="index"
          @click="seriesClick(index)">
          <img :src="item.seriesImg" alt=""></img>
        </div>
      </div>
    </div>
    <div class="welTitleBgDiv" :style="{ backgroundImage: `url(${furnish.welTitleBg})` }"></div>
    <div class="skuDetailBoxDivAll">
      <div class="skuDetailBoxItemDiv" v-for="(item,index) in seriesSkuFinalResult" :key="index" :id="`skuDetailBox${index}`" :style="{ backgroundColor: item.bgColor }">
        <img class="seriesTopicImg" :src="item.seriesTopicImg" alt=""></img>
        <div class="skuDetailBoxItemDiv1">
          <div class="seriesItemStep1ListDivAll">
          <img class="step1Img" :src="item.title1Img" alt=""></img>
          <div class="seriesItemStep1ListDiv">
            <div class="seriesItemStep1ItemDiv" v-for="(item1,index1) in item.step1List" :key="index1" @click="toast()">
            <img class="skuMainPicture" :src="item1.skuMainPicture" alt="" ></img>
          </div>
          </div>
        </div>
        <div class="seriesItemStep2ListDivAll">
          <img class="step2Img" :src="item.title2Img" alt=""></img>
          <div class="seriesItemStep2ListDiv">
            <div class="seriesItemStep2ItemDiv" v-for="(item1,index2) in item.step2List" :key="index2" @click="toast()">
            <img class="skuMainPicture" :src="item1.skuMainPicture" alt=""></img>
          </div>
          </div>
        </div>
        </div>
      </div>
    </div>
    <div class="zzSkuDiv" :style="{ backgroundImage: `url(${furnish.zzSkuBg})` }">
      <div class="draw-btn" :style="{ backgroundImage: `url('${getDrawBtnImg()}')` }" @click="toast()"></div>
    </div>
    <div class="goToShopDiv" :style="{ backgroundImage: `url(${furnish.goToShopBg})` }" @click="toast()"></div>
    <div>
      <!-- 非会员拦截 -->
      <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" />
      <VanPopup teleport="body" v-model:show="showRule">
        <RulePopup :rule="ruleTest" @close="showRule = false" />
      </VanPopup>
      <VanPopup teleport="body" v-model:show="showAward">
        <Award @closeDialog="showAward = false" />
      </VanPopup>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick, reactive } from "vue";
import dayjs from "dayjs";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { defaultSeriesSku } from "../ts/default";
import RulePopup from "../components/RulePopup.vue";
import usePostMessage from "@/hooks/usePostMessage";
import { showToast } from "vant";
import html2canvas from "html2canvas";
import OpenCard from "../components/OpenCard.vue";
import Award from '../components/Award.vue';

const showAward = ref(false);
const actInfo = {
  state: '2'
}
const getDrawBtnImg = () => {
  switch (actInfo.state) {
    case '1':
      return '//img10.360buyimg.com/imgzone/jfs/t1/225174/7/24635/21267/66d6b38aF394acd0e/9b0658f320fb5d92.png';
    case '2':
      return '//img10.360buyimg.com/imgzone/jfs/t1/96387/19/46699/22184/66d6b38aFdc123764/3495f55be5fa9334.png';
    default:
      return '//img10.360buyimg.com/imgzone/jfs/t1/247026/25/18195/38662/66d6b38aF9f158b4f/1b760a81b9570c13.png';
  }
};
const seriesClickIndex = ref(-1); // 点击的系列数据
const seriesSkuResult = ref<any>(null); // 商品数据
const seriesSkuFinalResult = ref<any>(null); // 商品数据
const seriesSku = ref(defaultSeriesSku);

const seriesClick = (index:number) => {
  seriesClickIndex.value = index;
  const element = document.getElementById('skuDetailBox'+index);
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
    });
  }
};

// 门槛弹窗
const showOpenCard = ref(false);
const activityData = inject("activityData") as any;
const decoData = inject("decoData") as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);

const isLoadingFinish = ref(false);

const shopName = ref("");
const showLimit = ref(false);
const showRule = ref(false);
const ruleTest = ref("");

const showRulePop = () => {
  showRule.value = true;
};
// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement("canvas");
    const ctx = cropCanvas.getContext("2d");
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(
      canvas,
      0,
      0,
      canvas.width,
      (canvas.width / 375) * 670,
      0,
      0,
      375,
      670
    );
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL("image/png");
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: "C",
        type: "screen",
        event: "sendScreen",
        data: blob,
      },
      "*"
    );
  });
};

const toast = () => {
  showToast("活动预览，仅供查看");
};
// 将装修的系列数据与设置的系列数据进行根据系列名字进行比对
const checkSkuDataEnd = () => {
  // console.log(seriesSkuResult.value, 'seriesSkuResult.value============');
  if (seriesSkuResult.value && seriesSkuResult.value.length > 0) {
    // 创建合并结果的Map（以ID为键）
    const mergedMap = new Map();
      // 遍历第一个数组seriesSkuResult
    seriesSkuResult.value.forEach((item:any) => {
      if (item.seriesName) {
        mergedMap.set(item.seriesName, { ...item }); // 浅拷贝避免引用
      }
    });
    // 遍历第二个数组进行合并
    furnish.seriesBoxTitleList.forEach((item:any) => {
      if (mergedMap.has(item.seriesName)) {
        // 合并已有对象（后者优先级更高）
        mergedMap.set(
          item.seriesName,
          { ...mergedMap.get(item.seriesName), ...item }
        );
      } else {
        mergedMap.set(item.seriesName, { ...item });
      }
    });
     // 转换为数组返回
     seriesSkuFinalResult.value = Array.from(mergedMap.values());
    //  console.log(Array.from(mergedMap.values()), '转换为数组返回============')
  }
};
const checkSeries = () => {
// 初始化结果对象  
  const result: any = {};
  console.log(seriesSku.value, 'seriesSku=========');

  if (seriesSku.value && seriesSku.value.length > 0) {
     // 遍历数据并分组  
  seriesSku.value.forEach((item:any) => {
    const seriesName = item.seriesName;
    // 初始化系列结构  
    if (!result[seriesName]) {
      result[seriesName] = {
        seriesName: item.seriesName,
        seriesImg: item.seriesImg,
        seriesTopicImg: item.seriesTopicImg,
        step1List: [],
        step2List: []
      };
    }
    item.skuList.forEach((item1:any) => {
      // 根据类型添加到对应列表 1 小样 2 正装
    if (parseInt(item1.type) === 1 && item1.status === '1') {
      result[seriesName].step1List.push(item1);
    } else if (parseInt(item1.type) === 2 && item1.status === '1') {
      result[seriesName].step2List.push(item1);
    }
    })
  });
  // 转换为数组格式  
  seriesSkuResult.value = Object.values(result);
  }
  checkSkuDataEnd();
  // console.log("转换后的数据结构：========", furnish, seriesSkuResult.value);
};

// 装修数据监听
registerHandler("deco", (data) => {
  // console.log(data, 'data===sadsddddddddddd');
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  checkSkuDataEnd();
  isLoadingFinish.value = true;
});
const showActivity = (data:any) => {
    endTime.value = dayjs(data.endTime).valueOf();
    startTime.value = new Date(data.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    if (data.seriesList && data.seriesList.length > 0) {
      seriesSku.value = data.seriesList.filter((item:any) => item.takeEffect);
    } else {
      seriesSku.value = defaultSeriesSku;
    }
    checkSeries();
    ruleTest.value = data.rules;
};
// 活动数据监听
registerHandler("activity", (data) => {
  // console.log("activity==========预览数据", data);
  showActivity(data);
  
});
// 截图监听
registerHandler("screen", () => {
  createImg();
});
checkSeries();
onMounted(() => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
    if (activityData) {
    showActivity(activityData);
  }
});

</script>
<style lang="scss" scoped>
.bg {
  min-height: 100vh;
  background-repeat: repeat-y;
  background-size: 100% 100%;
  padding-bottom: 0.5rem;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.2rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0rem;
  }

  .header-btn {
    width: 1rem;
    height: 0.36rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.stepDiv {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/329402/33/28100/121024/68ff305dF6bba3529/81e59119e85899fb.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.1rem;
  height: 2.76rem;
  margin-left: 50%;
  transform: translateX(-50%);
}

.seriesSkuBgAll {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/289669/12/8704/73606/68ff3050F1c75890e/af66d9d830413459.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.1rem;
  height: 7rem;
  margin-left: 50%;
  transform: translateX(-50%);
  margin-top: 0.4rem;
  display: flex;
  justify-content: center;
  padding-top: 1rem;

  .seriesSkuListBgDiv {
    height: 5.6rem;
    width: 6.4rem;
    overflow-y: scroll;
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-around;
    // align-items: center;
    .seriesSkuItemBgDiv {
      width: 1.5rem;
      margin: 0 0.3rem;

      img {
        width: 1.5rem;
      }
    }
  }
}

.welTitleBgDiv {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/246746/28/35253/30231/68ff305eF50e66c2c/3f86d137cfcde4e5.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 4.40rem;
  height: 1.08rem;
  margin-left: 50%;
  transform: translateX(-50%);
  margin-top: 0.4rem;
}
.skuDetailBoxDivAll{
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 0.7rem;
  .skuDetailBoxItemDiv{
    box-sizing: border-box;
    border: 0.04rem solid #ead09a;
    border-radius: 0.2rem;
    width: 7.08rem;
    margin-bottom: 0.8rem;
    .skuDetailBoxItemDiv1{}
    .seriesTopicImg{
      width: 100%;
      margin-top: -0.1rem;
    }
    .seriesItemStep1ListDivAll{
      .step1Img{
        height: 0.46rem;
        margin: 0.3rem auto 0.3rem auto;
      }
      .seriesItemStep1ListDiv{
      display: flex;
      flex-wrap: wrap;
      padding: 0 0.34rem;
      .seriesItemStep1ItemDiv{
        width: 3.15rem;
        height: 2.75rem;
        margin-bottom: 0.15rem;
        .skuMainPicture{
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      }
    }
    .seriesItemStep2ListDivAll{
        .step2Img{
          height: 0.46rem;
          margin: 0.3rem auto;
        }
      .seriesItemStep2ListDiv{
        display: flex;
        flex-wrap: wrap;
        padding: 0 0.34rem;
        .seriesItemStep2ItemDiv{
          width: 3.15rem;
          height: 2.75rem;
          margin-bottom: 0.15rem;
          .skuMainPicture{
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
      }
    }
  }
}
.zzSkuDiv{
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/330494/27/28183/121709/68ff305eFbf136418/de24d9a3e817f14e.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.1rem;
  height: 3.5rem;
  margin-left: 50%;
  transform: translateX(-50%);
  position: relative;
  margin-top: 0.4rem;
  .draw-btn {
      width: 1.1rem;
      height: 1.1rem;
      position: absolute;
      top: 1.16rem;
      right: 0.5rem;
      background: {
        repeat: no-repeat;
        size: contain;
      }
    }
}
.goToShopDiv{
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/353541/38/1549/54334/68ff305eFb6c501b6/3a1df79ae3517372.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.1rem;
  height: 0.96rem;
  margin-left: 50%;
  transform: translateX(-50%);
  margin-top: 0.4rem;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}

</style>
