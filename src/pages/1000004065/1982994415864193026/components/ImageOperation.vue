<template>
  <div class="mobile-image-composer">
    <!-- 预览区域 -->
    <div class="preview-container" ref="previewContainer">
      <canvas 
        ref="canvas"
        :class="{ 'canvas-draggable': props.layout === 'custom' }"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @touchcancel="handleTouchEnd"
      ></canvas>
      
      <div v-if="!props.image1.src || !props.image2.src" class="preview-placeholder">
        <p>请提供两张图片进行合成</p>
      </div>
    </div>

    <!-- 状态提示 -->
    <transition name="fade">
      <div v-if="statusMessage" :class="['status-message', statusClass]">
        {{ statusMessage }}
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import type { ComposerProps, ImageData } from './types'

// Props
interface Props extends ComposerProps {}
const props = withDefaults(defineProps<Props>(), {
  layout: 'horizontal',
  spacing: 20,
  backgroundColor: '#ffffff',
  width: 300,
  height: 400
})

// Emits
const emit = defineEmits<{
  'update:image1': [value: ImageData]
  'update:image2': [value: ImageData]
  'composed': [imageData: string]
}>()

// Refs
const canvas = ref<HTMLCanvasElement>()
const previewContainer = ref<HTMLDivElement>()
const compositeImage = ref<string>('')

// 状态
const dragState = reactive({
  isDragging: false,
  targetImage: null as number | null,
  startX: 0,
  startY: 0,
  imageStartX: 0,
  imageStartY: 0
})

const statusMessage = ref('')
const statusClass = ref('')

// 方法
const composeImages = () => {
  if (!props.image1.src || !props.image2.src || !canvas.value) {
    return
  }

  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  const img1 = new Image()
  const img2 = new Image()

  img1.onload = () => {
    img2.onload = () => {
      // 设置画布尺寸
      updateCanvasSize()
      
      // 清除画布
      ctx.fillStyle = props.backgroundColor
      ctx.fillRect(0, 0, canvas.value!.width, canvas.value!.height)

      if (props.layout === 'custom') {
        drawCustomLayout(ctx, img1, img2)
      } else {
        drawPresetLayout(ctx, img1, img2)
      }

      compositeImage.value = canvas.value!.toDataURL('image/png')
      emit('composed', compositeImage.value)
    }
    img2.src = props.image2.src
  }
  img1.src = props.image1.src
}

const drawCustomLayout = (ctx: CanvasRenderingContext2D, img1: HTMLImageElement, img2: HTMLImageElement) => {
  const scale1 = (props.image1.scale || 100) / 100
  const scale2 = (props.image2.scale || 100) / 100

  const img1Width = img1.width * scale1
  const img1Height = img1.height * scale1
  const img2Width = img2.width * scale2
  const img2Height = img2.height * scale2

  ctx.drawImage(img1, props.image1.x || 50, props.image1.y || 50, img1Width, img1Height)
  ctx.drawImage(img2, props.image2.x || 200, props.image2.y || 50, img2Width, img2Height)
}

const drawPresetLayout = (ctx: CanvasRenderingContext2D, img1: HTMLImageElement, img2: HTMLImageElement) => {
  const { layout, spacing } = props
  const canvasWidth = canvas.value!.width
  const canvasHeight = canvas.value!.height

  if (layout === 'horizontal') {
    const scale = Math.min(
      canvasHeight / Math.max(img1.height, img2.height),
      0.8
    )

    const img1Width = img1.width * scale
    const img2Width = img2.width * scale
    const imgHeight = Math.min(img1.height * scale, img2.height * scale)

    const totalWidth = img1Width + img2Width + spacing
    const startX = (canvasWidth - totalWidth) / 2
    const y = (canvasHeight - imgHeight) / 2

    ctx.drawImage(img1, startX, y, img1Width, imgHeight)
    ctx.drawImage(img2, startX + img1Width + spacing, y, img2Width, imgHeight)

  } else if (layout === 'vertical') {
    const scale = Math.min(
      canvasWidth / Math.max(img1.width, img2.width),
      0.8
    )

    const imgWidth = Math.min(img1.width * scale, img2.width * scale)
    const img1Height = img1.height * scale
    const img2Height = img2.height * scale

    const totalHeight = img1Height + img2Height + spacing
    const startY = (canvasHeight - totalHeight) / 2
    const x = (canvasWidth - imgWidth) / 2

    ctx.drawImage(img1, x, startY, imgWidth, img1Height)
    ctx.drawImage(img2, x, startY + img1Height + spacing, imgWidth, img2Height)

  } else if (layout === 'diagonal') {
    const scale1 = Math.min(0.4, canvasWidth / (img1.width * 1.5))
    const scale2 = Math.min(0.4, canvasWidth / (img2.width * 1.5))

    const img1Width = img1.width * scale1
    const img1Height = img1.height * scale1
    const img2Width = img2.width * scale2
    const img2Height = img2.height * scale2

    ctx.drawImage(img1, 20, 20, img1Width, img1Height)
    ctx.drawImage(img2, canvasWidth - img2Width - 20, canvasHeight - img2Height - 20, img2Width, img2Height)
  }
}

const updateCanvasSize = () => {
  if (!canvas.value || !previewContainer.value) return
  
  const containerWidth = previewContainer.value.clientWidth || props.width
  const containerHeight = Math.min(containerWidth * 1.3, window.innerHeight * 0.5) || props.height
  
  canvas.value.width = containerWidth
  canvas.value.height = containerHeight
}

// 触摸事件处理
const handleTouchStart = (event: TouchEvent) => {
  if (props.layout !== 'custom' || !canvas.value) return
  
  event.preventDefault()
  const touch = event.touches[0]
  const rect = canvas.value.getBoundingClientRect()
  
  const x = touch.clientX - rect.left
  const y = touch.clientY - rect.top
  
  // 检查触摸位置
  const img1 = new Image()
  const img2 = new Image()
  
  img1.onload = () => {
    img2.onload = () => {
      const scale1 = (props.image1.scale || 100) / 100
      const scale2 = (props.image2.scale || 100) / 100
      
      const img1Width = img1.width * scale1
      const img1Height = img1.height * scale1
      const img2Width = img2.width * scale2
      const img2Height = img2.height * scale2
      
      if (x >= (props.image1.x || 50) && x <= (props.image1.x || 50) + img1Width &&
          y >= (props.image1.y || 50) && y <= (props.image1.y || 50) + img1Height) {
        startDrag(1, x, y)
      } else if (x >= (props.image2.x || 200) && x <= (props.image2.x || 200) + img2Width &&
                 y >= (props.image2.y || 50) && y <= (props.image2.y || 50) + img2Height) {
        startDrag(2, x, y)
      }
    }
    img2.src = props.image2.src
  }
  img1.src = props.image1.src
}

const handleTouchMove = (event: TouchEvent) => {
  if (!dragState.isDragging || !canvas.value) return
  
  event.preventDefault()
  const touch = event.touches[0]
  const rect = canvas.value.getBoundingClientRect()
  
  const x = touch.clientX - rect.left
  const y = touch.clientY - rect.top
  
  const deltaX = x - dragState.startX
  const deltaY = y - dragState.startY
  
  if (dragState.targetImage === 1) {
    const newX = Math.max(0, Math.min(canvas.value.width - 50, dragState.imageStartX + deltaX))
    const newY = Math.max(0, Math.min(canvas.value.height - 50, dragState.imageStartY + deltaY))
    emit('update:image1', { ...props.image1, x: newX, y: newY })
  } else if (dragState.targetImage === 2) {
    const newX = Math.max(0, Math.min(canvas.value.width - 50, dragState.imageStartX + deltaX))
    const newY = Math.max(0, Math.min(canvas.value.height - 50, dragState.imageStartY + deltaY))
    emit('update:image2', { ...props.image2, x: newX, y: newY })
  }
}

const handleTouchEnd = () => {
  dragState.isDragging = false
  dragState.targetImage = null
}

const startDrag = (targetImage: number, startX: number, startY: number) => {
  dragState.isDragging = true
  dragState.targetImage = targetImage
  dragState.startX = startX
  dragState.startY = startY
  
  if (targetImage === 1) {
    dragState.imageStartX = props.image1.x || 50
    dragState.imageStartY = props.image1.y || 50
  } else {
    dragState.imageStartX = props.image2.x || 200
    dragState.imageStartY = props.image2.y || 50
  }
}

const showStatus = (message: string, type: 'success' | 'error') => {
  statusMessage.value = message
  statusClass.value = type
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}

// 生命周期
onMounted(() => {
  updateCanvasSize()
  window.addEventListener('resize', updateCanvasSize)
  
  // 初始合成
  if (props.image1.src && props.image2.src) {
    nextTick(() => {
      composeImages()
    })
  }
})

// 监听器
watch(
  () => [props.image1.src, props.image2.src, props.layout, props.spacing, props.backgroundColor],
  () => {
    if (props.image1.src && props.image2.src) {
      nextTick(() => {
        composeImages()
      })
    }
  },
  { deep: true }
)

watch(
  () => [props.image1.x, props.image1.y, props.image1.scale, props.image2.x, props.image2.y, props.image2.scale],
  () => {
    if (props.layout === 'custom' && props.image1.src && props.image2.src) {
      composeImages()
    }
  },
  { deep: true }
)
</script>

<style scoped>
.mobile-image-composer {
  width: 100%;
  height: 100%;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

canvas {
  max-width: 100%;
  max-height: 100%;
  display: block;
}

.canvas-draggable {
  cursor: grab;
  touch-action: none;
}

.canvas-draggable:active {
  cursor: grabbing;
}

.preview-placeholder {
  text-align: center;
  color: #999;
  font-size: 1rem;
  padding: 20px;
}

/* 状态提示 */
.status-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-message.success {
  background: #48bb78;
  color: white;
}

.status-message.error {
  background: #f56565;
  color: white;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 防止点击高亮 */
* {
  -webkit-tap-highlight-color: transparent;
}

/* 优化移动端触摸 */
canvas {
  touch-action: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .preview-container {
    min-height: 250px;
  }
}
</style>