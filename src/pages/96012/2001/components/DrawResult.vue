<template>
  <div class="drawResultDivAll">
    <div class="drawSuccessDiv" v-if="drawResultData && drawResultData.prizeType > 0 && drawResultData.prizeType !== 7">
      <div class="closeDiv" @click="closeClick()"></div>
      <div class="prizeImgDiv">
        <img :src="drawResultData.prizeImg" alt=""></img>
      </div>
      <div class="prizeType3TextDiv" v-if="drawResultData.prizeType === 3">请在活动时间内填写完毕收货信息，逾期未填写将视为 用户主动放弃该奖品权益</div>
      <div class="prizeType3TextDiv" v-else-if="drawResultData.prizeType === 1">已发放到您的账户 京东-我的-我的钱包-优惠券 中查看</div>
      <div class="prizeType3TextDiv" v-else-if="drawResultData.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</div>
      <div class="prizeType3TextDiv" v-else-if="drawResultData.prizeType === 8">京东E卡已发放到您的账户中 京东-我的-我的钱包-礼品卡 中查看</div>
      <div class="btnDivAll">
       <div v-if="drawResultData.prizeType === 3" @click="addressClick()">填写收货地址</div> 
       <div v-else @click="closeClick()">确认</div>
      </div>
    </div>
    <div class="drawSuccessDiv7" v-else-if="drawResultData && drawResultData.prizeType === 7">
      <div class="closeDiv" @click="closeClick()"></div>
      <div class="prizeImgDiv">
        <img :src="drawResultData.prizeImg" alt=""></img>
      </div>
      <div class="giftDivAll" v-if="drawResultData.result && drawResultData.result.cardNumber">
        <div>卡号</div>
        <div class="giftRightDiv">
          <div class="cardDiv">{{drawResultData.result.cardNumber}}</div>
          <div class="copy-btn" :copy-text="drawResultData.result.cardNumber">复制</div>
        </div>
      </div>
      <div class="giftDivAll" v-if="drawResultData.result && drawResultData.result.cardPassword">
        <div>卡密</div>
        <div class="giftRightDiv">
          <div class="cardDiv">{{drawResultData.result.cardPassword}}</div>
          <div class="copy-btn" :copy-text="drawResultData.result.cardPassword">复制</div>
        </div>
      </div>
      <div class="prizeType3TextDiv" v-if="drawResultData.prizeType === 7">请在活动时间内及时兑换卡密奖品，逾期未填写将视为 用户主动放弃该奖品权益</div>
      <div class="btnDivAll">
       <div @click="closeClick()">确认</div>
      </div>
    </div>
    <div v-else class="drawFailDiv">
      <div class="closeDiv" @click="closeClick()"></div>
      <div class="btnKnowDiv" @click="closeClick()"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps({
  drawResultData: {
    type: Object,
    default: {
      prizeType: 0,
      prizeImg: '',
      result: null,
    },
  },
});
const emits = defineEmits(["close","saveAddress"]);
const closeClick = () => {
  emits("close", null);
};
const addressClick = () => {
  emits("saveAddress", {addressId: '',activityPrizeId: '',prizeRecordId: '', writeAddress: false});
};
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>
<style scoped lang="scss">
.drawResultDivAll {
  .drawSuccessDiv {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/356679/28/4495/13732/690c0245F856a06d2/c942dcb5bbaf92f3.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 6.5rem;
    height: 7.88rem;
    padding-top: 2.2rem;
    .closeDiv {
      position: absolute;
      right: 0rem;
      top: 0rem;
      width: 0.8rem;
      height: 0.8rem;
      // background-color: red;
    }
    .prizeImgDiv{
      width: 5.90rem;
      height: 3rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 50%;
      transform: translateX(-50%);
      img{
        height: 2rem;
        width: auto;
        object-fit: contain;
      }
    }
    .prizeType3TextDiv{
      color: #b4b4b4;
      font-size: 0.24rem;
      width: 5.90rem; 
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 0.24rem;
    }
    .btnDivAll{
      position: absolute;
      bottom: 0.42rem;
      width: 4rem;
      height: 1rem;
      border-radius: 0.5rem;
      background-color: #000;
      color: #fff;
      font-size: 0.36rem;
      display: flex;
      align-items: center;
      justify-content: center;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .drawSuccessDiv7{
     background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/358609/28/3625/15059/690c0e52F70081208/d69d1f2e25e0e8ad.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 6.5rem;
    height: 9.98rem;
    padding-top: 2.2rem;
    .closeDiv {
      position: absolute;
      right: 0rem;
      top: 0rem;
      width: 0.8rem;
      height: 0.8rem;
      // background-color: red;
    }
    .prizeImgDiv{
      width: 5.90rem;
      height: 3rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-bottom: 0.18rem;
      img{
        height: 2rem;
        width: auto;
        object-fit: contain;
      }
    }
    .giftDivAll{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0.08rem 0 0.27rem;
      width: 5.9rem;
      height: 1rem;
      border-radius: 0.15rem;
      background-color: #fbf6ed;
      margin-left: 50%;
      transform: translateX(-50%);
      font-size: 0.3rem;
      color: #000;
      margin-bottom: 0.18rem;
      .giftRightDiv{
        display: flex;
        align-items: center;
      }
      .cardDiv{
        font-size: 0.24rem;
        color: #999999;
      }
      .copy-btn{
        border-radius: 0.1rem;
        background-color: #facd89;
        color: #333333;
        font-size: 0.24rem;
        border: 0.01rem solid #909090;
        width: 0.78rem;
        height: 0.44rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 0.08rem;
      }
    }
    .prizeType3TextDiv{
      color: #b4b4b4;
      font-size: 0.24rem;
      width: 5.90rem; 
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 0.24rem;
    }
    .btnDivAll{
      position: absolute;
      bottom: 0.42rem;
      width: 4rem;
      height: 1rem;
      border-radius: 0.5rem;
      background-color: #000;
      color: #fff;
      font-size: 0.36rem;
      display: flex;
      align-items: center;
      justify-content: center;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .drawFailDiv {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/350465/20/23433/25005/690c0246Fd4c8022b/b21cbde95a96b5eb.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 6.5rem;
    height: 7.88rem;
    .closeDiv {
      position: absolute;
      right: 0rem;
      top: 0rem;
      width: 0.8rem;
      height: 0.8rem;
      // background-color: red;
    }
    .btnKnowDiv {
      position: absolute;
      width: 4rem;
      height: 1rem;
      left: 50%;
      transform: translateX(-50%);
      bottom: 1.1rem;
      // background-color: aqua;
    }
  }
}
</style>
