<template>
  <div class="rule-bk">
    <div class="close" @click="close"/>
    <div class="content" v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps(['rule']);

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 6.5rem;
  height: 10.53rem;
  position: relative;
  padding-top: 1.7rem;
  padding-left: 0.36rem;
  padding-right: 0.36rem;
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/340150/35/27376/12924/690b485dF4606996e/5f13e198a2cf1631.png) no-repeat;
  background-size: 100%;

  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.79rem;
    height: 0.79rem;
  }

  .content {
    height: 8.5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    line-height: 0.35rem;
    color: #666666;
    word-wrap: break-word;
    white-space: pre-wrap;
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 和 Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari 和 Opera */
    }
  }
}
</style>
