/**
 * @Description:caoshijie
 * @Date: 2024/10/23
 * @Description:OPPO 开门大吉
 * @FilePath:src\pages\1000004065\1879426668391612418\index.ts
 */
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, checkStatus, clipboardText } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import './tailwind.scss';
import 'swiper/swiper.min.css';

initRem();
const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};

init(config).then(async ({ baseInfo, pathParams }) => {
  checkStatus(baseInfo, true, true);
  // 设置页面title
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.use(clipboardText);
  app.mount('#app');
});
