import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import { httpRequest } from '@/utils/service';
import constant from '@/utils/constant';
import { CLIENT_TYPE, getClientType, isPC } from '@/utils/platforms/clientType';
import { robotPostMessage } from '@/utils/robot';
import dayjs from 'dayjs';

initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  disableNotice: true,
  shopId: '1000013402',
  activityMainId: '1872527708524322818',
};

const getParams = (key: string): string => {
  const result = new URLSearchParams(window.location.search);
  return result.get(key) as string;
};

let nowTime = dayjs().valueOf();

if (getParams('mockTime')) {
  nowTime = dayjs(getParams('mockTime')).valueOf();
}

const verifyUser = async () => {
  try {
    const { data } = await httpRequest.post('/10112/verifyUser');
    if (data) {
      throw new Error('封控用户');
    }
  } catch (error: any) {
    console.error(error);
    window.location.href = constant.LZ_LOST_PAGE_URL;
  }
};

function isMobileBrowser() {
  // 定义常见移动设备的关键字
  const mobileKeywords = ['Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 'Windows Phone', 'Opera Mini', 'IEMobile'];

  // 遍历关键字数组，判断 userAgent 中是否包含这些关键字
  return mobileKeywords.some((keyword) => navigator.userAgent.includes(keyword));
}

const isApp = window.jmfe.isApp('jd');
const isWechat = getClientType() === CLIENT_TYPE.WECHAT;

if (!isApp && !isWechat && (isPC() || isMobileBrowser()) && process.env.NODE_ENV === 'production' && !getParams('debug')) {
  const accessUrl = encodeURIComponent(window.location.href);
  window.location.href = `https://lzkjdz-isv.isvjcloud.com/prod/cc/custom/landing/openAppPage2/?actlink=${accessUrl}`;
} else {
  init(config).then(async ({ baseInfo, pathParams }) => {
    await verifyUser();
    app.provide('nowTime', nowTime);
    app.provide('baseInfo', baseInfo);
    app.provide('pathParams', pathParams);
    app.mount('#app');
  }).catch(async (error) => {
    await robotPostMessage('一起下单伊利赢免单', error.message);
  });
}
