<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">恭喜中奖</div>
      <div class="rule-box">
        <div class="prize-name">恭喜你成功抽中<br />【{{ prizeInfo.prizeName }}】</div>
        <img class="prize-img" :src="prizeInfo.prizeImg" alt="" />
        <template v-if="prizeInfo.prizeType === 3">
          <img class="btn" @click="handleAddress" src="//img10.360buyimg.com/imgzone/jfs/t1/358036/17/2211/16121/690823d1F15456387/9698849d0cdf44d7.png" />
          <div class="tips">请在中奖后1小时内填写地址，过期视作放弃奖品</div>
        </template>
        <template v-else>
          <img class="btn" @click="closeDialog" src="//img10.360buyimg.com/imgzone/jfs/t1/350974/37/21402/13964/69082699F447d5bb0/01842b2ff95559f5.png" />
          <div class="tips">京豆已放入您的账户中，可在<span>个人中心-京豆中</span>查看</div>
        </template>
      </div>
    </div>
  </popup>
  <SaveAddress :addressId="addressId" :isShowPopup="isShowSaveAddress" @closeDialog="isShowSaveAddress = false" />
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import SaveAddress from './SaveAddress.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  prizeInfo: {
    type: Object,
    default: () => ({}),
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const opendDialog = async () => {};
const isShowSaveAddress = ref(false);
const addressId = ref('');
const handleAddress = () => {
  console.log('handleAddress');
  addressId.value = props.prizeInfo.result.result;
  isShowSaveAddress.value = true;
  emits('closeDialog');
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  text-align: center;
  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    font-weight: bold;
    background: radial-gradient(circle, #4a3789 0%, #ebe9ed 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .rule-box {
    margin-top: 0.4rem;
    max-height: 6.2rem;
    .prize-name {
      font-weight: bold;
      font-size: 0.48rem;
      font-style: italic;
      color: #543b72;
    }
    .prize-img {
      display: block;
      width: 100%;
      height: 3rem;
      object-fit: contain;
    }
    .btn {
      width: 4.24rem;
      display: block;
      margin: 0.1rem auto;
    }
    .tips {
      font-family: 'OPPOSansR';
      font-size: 0.24rem;
      color: #363636;
      span {
        color: #65399a;
      }
    }
  }
}
</style>
