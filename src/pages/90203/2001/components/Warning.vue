<template>
  <div>
    <div class='warning'>
      <img class="warning-bg" :src="decoData.interceptPop" alt="">
      <div class="bindCard" @click="bindCard"></div>
      <div class="bindClose" @click="close"></div>
   </div>
  </div>
</template>

<script lang='ts' setup>
import { inject } from 'vue';
const decoData = inject('decoData') as any;
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
const bindCard = () => {
  window.open('https://o.jd.com/', '_blank');
};
</script>

<style scoped lang='scss'>
.warning {
  width: 80%;
  margin: 0 auto;
  position: relative;
  .warning-bg {
    width: 100%;
    object-fit: contain;
  }

  .bindCard {
    width: 100%;
    height: .8rem;
    position: absolute;
    bottom: .9rem;
  }
  .bindClose {
    width: 100%;
    height: .8rem;
    position: absolute;
    bottom: 0;
  }

}
.close-box {
  display: flex;
  width: 80%;
  margin: 0 auto;
  justify-content: flex-end;
  .close {
    border: 1px solid #fff;
    padding: 0.1rem;
    border-radius: 50%;
  }
}
</style>
