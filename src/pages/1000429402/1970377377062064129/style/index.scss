@font-face {
  font-family: 'FZYASHS';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZYASHS/FZYASHS_XIAN--GB1-0.woff');
}

@font-face {
  font-family: 'MFJinHuaNoncommercial';
  src: url('https://lzcdn.dianpusoft.cn/fonts/MFJinHuaNoncommercial/MFJinHuaNoncommercial-Regular.woff');
}

* {
  box-sizing: border-box;
}

.touch-scroll {
  overflow: auto;
  -webkit-overflow-scrolling: touch; /* 流畅滚动 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.touch-scroll::-webkit-scrollbar {
  width: 0;
  height: 0;
}

html {
  max-width: 750px;
  margin: 0 auto;
  background: #FFFFFF;
  overflow-x: hidden;

  .background {
    width: 7.5rem;
    height: 18.23rem;
    position: relative;
    text-align: center;
    background: {
      repeat: no-repeat;
      size: contain;
    };

    .kv-module {
      width: 7.5rem;
      height: 5.44rem;
      position: relative;
      margin: 0 auto;
      background: {
        repeat: no-repeat;
        size: 100%;
      };

      .user-info-view {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: .5rem;
        top: .5rem;

        .user-img {
          width: 1.04rem;
          height: 1.04rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background: {
            repeat: no-repeat;
            size: contain;
            image: url("//img10.360buyimg.com/imgzone/jfs/t1/346086/31/5028/4578/68cbc26aF5a309550/e8897166e65842b5.png");
          };

          img {
            border-radius: 50%;
            width: 80%;
          }
        }

        .user-info {
          color: #FFFFFF;

          .user-nick {
            width: 1.4rem;
            font-size: .3rem;
            font-weight: bold;
          }

          .user-level {
            font-size: .22rem;
          }
        }
      }

      .pos-view {
        position: absolute;
        left: .22rem;
        top: 2.5rem;
        text-align: center;

        .user-point-view {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2.7rem;
          color: #FFFFFF;
          font-weight: bold;


          border-bottom: 1px solid #FFFFFF;

          .user-level {
            width: 0.91rem;
            height: 0.91rem;
            line-height: 0.94rem;
            font-size: .44rem;
            text-align: center;
            background: {
              repeat: no-repeat;
              size: contain;
              image: url("//img10.360buyimg.com/imgzone/jfs/t1/349073/2/4864/3401/68cbc84eF5335a4b8/a117b83edb4c7b85.png");
            };
          }

          .user-point {
            font-size: .27rem;
            margin-left: .2rem;
          }
        }
      }

      .ip-img {
        width: 3.6rem;
        position: absolute;
        right: .4rem;
        top: .4rem;
      }
    }

    .tab-module {
      position: relative;
      width: 7.5rem;
      height: 2.16rem;
      margin-top: -.5rem;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: {
        repeat: no-repeat;
        size: contain;
      };

      .tab-icon-item {
        width: .96rem;
        padding-bottom: .15rem;
      }
    }

    .profile-module {
      position: relative;
      width: 7.5rem;

      .tab-icon-view {
        height: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-around;
      }

      .list-view {
        height: 7.6rem;
        padding: 0 .6rem;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        overflow-y: auto;

        .list-item {
          width: 45%;
          height: 3.8rem;
          position: relative;

          .ip-bg {
            width: 2.64rem;
            height: 2.8rem;
            text-align: center;
            margin: 0 auto;
            position: relative;
            background: {
              repeat: no-repeat;
              size: contain;
            };

            .ip-img {
              width: 1.4rem;
              max-height: 1.9rem;
              margin: 0 auto 0;
            }

            .ip-name {
              width: 80%;
              text-align: center;
              line-height: .4rem;
              font-size: .26rem;
              color: #FFFFFF;
              position: absolute;
              left: 50%;
              bottom: .36rem;
              transform: translateX(-50%);
            }
          }

          .ip-btn {
            width: 2.3rem;
            height: .68rem;
            line-height: .68rem;
            position: absolute;
            bottom: .44rem;
            left: 50%;
            text-align: center;
            color: #FFFFFF;
            padding-left: .04rem;
            font-size: .32rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: translateX(-50%);
            cursor: pointer;
            background: {
              repeat: no-repeat;
              size: contain;
            };
          }

          .bg-bg {
            width: 2.12rem;
            height: 2.48rem;
            text-align: center;
            justify-content: center;
            padding-top: .1rem;
            margin: 0 auto;
            position: relative;
            display: flex;
            align-items: center;
            background: {
              repeat: no-repeat;
              size: contain;
            };

            .bg-img {
              width: 85%;
            }
          }

          .sku-bg {
            width: 2.64rem;
            height: 2.74rem;
            text-align: center;
            justify-content: center;
            padding-top: .1rem;
            padding-bottom: .6rem;
            margin: 0 auto;
            position: relative;
            display: flex;
            align-items: center;
            background: {
              repeat: no-repeat;
              size: contain;
            };

            .sku-img {
              width: 55%;
            }

            .sku-name {
              display: flex;
              flex-wrap: nowrap;
              text-align: center;
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              top: 0;
            }
          }
        }
      }
    }

    .pointsGuide-module {
      width: 7rem;
      height: 2.26rem;
      margin: 0 auto;
      position: relative;
      background: {
        repeat: no-repeat;
        size: contain;
      };

      .hotZone-item {
        position: absolute;
        //background: rgba(0, 0, 0, 0.5);
        color: #fff;
      }
    }

  }
}

.gray {
  filter: grayscale(1);
}

/*超过一行显示省略号*/
.one-line-omit {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

// 超过两行显示省略号
.two-line-omit {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.none-data-tip {
  text-align: center;
  font-size: 0.33rem;
  color: #222222;
  line-height: 1.2rem;
}
