<template>
  <div>
    <div class="close-box">
      <img alt=""  src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
      </div>
    <div class='my-prize' v-if="myPrizeList.length === 0">
      <img class="my-prize-bg" src="//img10.360buyimg.com/imgzone/jfs/t1/332865/37/25271/32644/68f5b245F13c7e93b/720a6bd409fd6024.png" alt="">
    </div>
   <div v-else>
    <div class="my-prize" >
      <img class="my-prize-bg" src="//img10.360buyimg.com/imgzone/jfs/t1/333051/36/25349/68501/68f5b246Fd72a018e/2fad27a2cd469dde.png" alt="">
      <div class="my-prize-content">
        <div class="my-prize-item" v-for="(item, index) in myPrizeList" :key="index">
          <div class="my-prize-item-time">{{ dayjs(item.createTime).format('MM-DD HH:mm:ss') }}</div>
          <div class="my-prize-item-name">{{ item.prizeName }}</div>
          <div class="my-prize-item-status" v-if="item.prizeType == 7">
            <div v-if="item.status === 1" @click="showCardNum(item)" class="my-prize-item-status-btn">查看卡密</div>
            <div v-else style="color: #ff0500;">发放失败</div>
          </div>
          <div class="my-prize-item-status" v-else :style="{ color: item.status === 1 ? '#7c2929' : '#ff0500' }">{{ item.status === 1 ? '发放成功' : '发放失败' }}</div>
         
        </div>
      </div>
    </div>
   </div>
   <div>{{ myPrizeList.length }}</div>
  </div>
</template>

<script lang='ts' setup>
import dayjs from 'dayjs';
const props = defineProps(['myPrizeList']);

console.log(props.myPrizeList);

const emits = defineEmits(['close', 'showCardNum']);

const close = () => {
  emits('close');
};

const showCardNum = (item: any) => {
  item.result = item.prizeContent;
  emits('showCardNum', item);
};
</script>

<style scoped lang='scss'>
.my-prize {
  width: 80%;
  margin: 0 auto;
  position: relative;
  .my-prize-bg {
    width: 100%;
    object-fit: contain;
  }
  .my-prize-content {
    overflow-y: auto;
    width: 100%;
    height: 4.9rem;
    box-sizing: border-box;
    padding: 0 0.4rem;
    position: absolute;
    top: 1.8rem;
    color: #7c2929;
    word-wrap: break-word;
    


    .my-prize-item {
      display: grid;
      grid-template-columns: 1.3rem 1fr 1.4rem;
      font-size: 0.23rem;

      gap: .16rem;
      margin-bottom: 0.18rem;
      align-items: center;

      .my-prize-item-time {
        text-align: center;
        color: #ababab;
        line-height: 1.3;
      }
      .my-prize-item-name {
        text-align: center;
        max-width: 1.7rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .my-prize-item-status {
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .my-prize-item-status-btn {
        width: 1.2rem;
        height: 0.55rem;
        text-align: center;
        border-radius: 0.4rem;
        border: 1px solid #fff;
        background: linear-gradient(to bottom, #ff3621, #ff6758);
        box-shadow: 0 4px 6px 0 rgba(255, 80, 38, 0.33);
        color: #fff;
        font-size: 0.24rem;
      
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

}
.close-box {
  display: flex;
  width: 80%;
  margin: 0 auto;
  justify-content: flex-end;
  .close {
    border: 1px solid #fff;
    padding: 0.1rem;
    border-radius: 50%;
  }
}
</style>
