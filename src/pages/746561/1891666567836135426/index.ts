import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import App from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import 'animate.css';
import '@/style';
import { setToastDefaultOptions, allowMultipleToast } from 'vant';

// 全局设置loading toast配置
setToastDefaultOptions('loading', {
  forbidClick: true,
  duration: 0,
  message: '请稍候',
});
// 全局设置 普通toast配置
setToastDefaultOptions({
  duration: 2000,
  forbidClick: true,
});
// 允许多个toast同时展示
allowMultipleToast();
// 页面自适应
initRem(750);

const app = createApp(App);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};

init(config)
  .then(({
    baseInfo,
    pathParams,
    userInfo,
  }) => {
    // 设置页面title
    document.title = baseInfo?.activityName || '未命名';
    app.provide('baseInfo', baseInfo);
    app.provide('pathParams', pathParams);
    app.provide('baseUserInfo', userInfo);
    app.use(EventTrackPlugin, {});
    app.mount('#app');
  });
