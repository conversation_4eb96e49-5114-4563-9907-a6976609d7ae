import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: false,
  backActRefresh: false,
  disableNotice: true,
};

init(config).then(({ baseInfo, pathParams }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
