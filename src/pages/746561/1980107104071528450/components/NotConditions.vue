<!-- 不符合条件弹窗 -->

<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false">
    <div class="dialog" @click="gotoShopPage(baseInfo.shopId)"></div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.49rem;
  height: 8.42rem;
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/243925/37/35637/70888/68f7518cFf5bf8127/1b824d4b79b4b189.png) no-repeat;
  background-size: contain;
  padding: 0.25rem 0.25rem;
  box-sizing: border-box;
  text-align: center;
  color: #724515;

  .close_icon {
    position: absolute;
    right: 0.3rem;
    top: 0.1rem;
  }

  .title {
    font-size: 0.4rem;
    font-weight: bold;
  }
  .list-til {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.4rem 0 0;
    .list-til-item {
      flex: 1;
    }
  }
  .item-box {
    max-height: 6.2rem;
    overflow-y: auto;
    .list-item {
      margin: 0 0 0.2rem;
      div {
        flex: 1;
      }
    }
  }
  .close_icon {
    width: 1rem;
    height: 1rem;
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
