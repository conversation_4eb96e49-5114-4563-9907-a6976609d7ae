<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">我的合拍</div>
      <div class="rule-box">
        <div class="item-box" v-if="myPhotos.length">
          <div class="list-item" v-for="item in myPhotos">
            <div>
              <img :src="item.img" alt="" />
            </div>
          </div>
        </div>
        <div class="no-prize" v-else>暂无数据</div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getRules } from '../script/ajax';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const myPhotos = ref<
  {
    img: string;
  }[]
>([]);
const opendDialog = async () => {
  console.log('🚀 ~ opendDialog ~ opendDialog:');
  //   myPhotos.value = await getRules();
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  text-align: center;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    .item-box {
      max-height: 6.1rem;
      overflow-y: auto;
      margin: 0.3rem auto;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.2rem;
      .list-item {
        background: url(../assets/surprise/gift-bg.png) no-repeat;
        background-size: contain;
        width: 2.14rem;
        height: 2.36rem;
        border-radius: 0.06rem;
        border: solid 0.01rem #ffffff;
        margin: 0 auto;
        padding: 0.3rem 0.5rem;
        box-sizing: border-box;
        div {
          flex: 1;
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
      }
    }
    .no-prize {
      font-size: 0.4rem;
      text-align: center;
      margin: 1.5rem auto 0;
    }
  }
}
</style>
