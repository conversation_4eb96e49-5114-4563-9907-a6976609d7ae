<template>
  <div class="rule-bk" :style="{backgroundImage:`url(${decoData.ruleBg})`}" >
    <div class="rule">
      <div v-html="rule"></div>
    </div>
  </div>
<!--  <div class="close" :style="{backgroundImage:`url(${decoData.close})`}" @click="closeRule"></div>-->
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { inject, ref } from 'vue';

const emits = defineEmits(['close']);
const decoData = inject('decoData') as any;
console.log(decoData.ruleBg);
const rule = ref('');
const closeRule = () => {
  emits('close');
};

const getRule = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const res = await httpRequest.get('/common/getRule');
    rule.value = res.data;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
getRule();
</script>

<style scoped lang="scss" >
::-webkit-scrollbar {
  display: none;
}
.rule-bk {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 5.7rem;
  height: 7.88rem;
  padding-top: 1.6rem;
  padding-bottom: 0.3rem;
}
.rule {
  height: 100%;
  padding: 0 0.35rem;
  font-size: 0.24rem;
  color: #710000;
  white-space: pre-wrap;
  word-break: break-all;
  div {
    height: 100%;
    overflow-y: scroll;
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  background-repeat: no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
