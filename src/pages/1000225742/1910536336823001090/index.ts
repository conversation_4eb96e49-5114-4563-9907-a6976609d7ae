/**
 * @Description:caoshijie
 * @Date: 2025年4月11日
 * @Description:星巴克  定制积分
 * @FilePath:src\pages\1000225742\1910536336823001090.ts
 */
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, checkStatus, clipboardText } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem(375);

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};

init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
  checkStatus(baseInfo, true, true);
  // 设置页面title
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('userBaseInfo', userInfo);
  app.provide('pathParams', pathParams);
  app.use(clipboardText);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
