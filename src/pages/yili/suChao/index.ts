import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import { initActive } from './hooks';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  // disableShare: true,
  shopId: '1000013402',
  // activityMainId: '1947670441995603969',
};

init(config).then(async ({ baseInfo, pathParams }) => {
  // 设置页面title
  document.title = '苏超伊触即发';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  await initActive();
  app.mount('#app');
});
// app.mount('#app');
