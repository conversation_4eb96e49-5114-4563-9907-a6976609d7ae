/**
 * @Description:caoshijie
 * @Date: 2025年10月28日
 * @Description:oppo琦妙星光派对
 * @FilePath: src\pages\1000004065\1982994415864193026
 */
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';

import EventTrackPlugin from '@/plugins/EventTracking';
initRem();
const app = createApp(root);
import 'swiper/swiper.min.css';

// 初始化页面
const config: InitRequest = {
  backActRefresh: false,
  disableThreshold: true,
  thresholdPopup: true,
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('decoData', decoData);
  app.use(EventTrackPlugin, {});

  app.mount('#app');
});
