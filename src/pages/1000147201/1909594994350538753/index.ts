/*
 * @Description: 林某
 */
import '@/style/reset.scss';
import { createApp } from 'vue';
import { init } from '@/utils';
import './style/index.scss';
import { initRem } from '@/utils/client';
import '@/style';
import root from './App.vue';
import { InitRequest } from '@/types/InitRequest';

initRem(750);

// 设置页面title
document.title = '定制活动';

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  // disableShare: true,
  shopId: '1000147201',
  activityMainId: '1909594994350538753',
};

// 初始化页面,
init(config).then(({ baseInfo, pathParams }) => {
  app.provide('baseInfo', baseInfo);
  app.mount('#app');
});
