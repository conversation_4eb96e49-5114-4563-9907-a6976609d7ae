<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>透明视频播放示例</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      /* 网页背景（会透过视频透明部分显示） */
      background: url(bg.jpeg);
      background-size: 100% 100%;
      height: 100vh;
      color: white;
    }

    video {
      width: 100%;
      object-fit: contain;
      display: block;
    }
  </style>
</head>

<body>
<div class="video-container">
  <!-- 透明视频 -->
  <div>video/webm</div>
  <video loop muted controls autoplay playsinline
         webkit-playsinline
         disablePictureInPicture
         controlsList="nodownload nofullscreen"
         x5-playsinline
         x5-video-player-type="h5"
         x5-video-orientation="portrait">
    <source src="output.webm" type="video/webm">
    您的浏览器不支持WebM视频格式
  </video>
  <div>video/mp4</div>
  <!-- 透明视频 -->
  <video loop muted controls autoplay playsinline
         webkit-playsinline
         disablePictureInPicture
         controlsList="nodownload nofullscreen"
         x5-playsinline
         x5-video-player-type="h5"
         x5-video-orientation="portrait">
    <source src="output.mp4" type="video/mp4">
    您的浏览器不支持mp4视频格式
  </video>
  <div>image/apng</div>
  <!-- 透明视频 -->
  <img style="display: block;margin: auto;background: transparent;object-fit: cover;width: 323px;"
       src="output.apng" width="503" height="333">
</div>
</body>

</html>
