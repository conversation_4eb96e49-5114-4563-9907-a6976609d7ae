<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="officialTitleBox">
      <img class="officialTitleImg" :src="furnish.officialTitleImg" alt="">
    </div>
    <div class="officialTabArea">
      <div class="officialTabWrapper" v-for="(tab, index) in officialTabs" :key="index">
        <div class="officialTab"
          :style="index === activeOfficialTab ? furnishStyles.officialTabTextActiveColor.value : furnishStyles.officialTabTextColor.value"
          @click="switchOfficialTab(index)"
        >
          {{tab.officialTabText}}
        </div>
        <!-- 选中指示器 -->
        <div
          class="tabIndicator"
          v-if="index === activeOfficialTab"
          :style="{ backgroundColor: furnishStyles.officialTabTextActiveColor.value.color }"
        ></div>
      </div>
    </div>
    <div class="imgListBox">
      <ImagesList :imgList="currentImgList" :showUserInfo="false" :showRank="false" :showChangeShare="false"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../../ts/furnishStyles';
import {defineProps, ref, computed} from "vue";
import ImagesList from "../../components/ImagesList.vue";

// 定义数据接口
interface OfficialExample {
  fileUrlList: string[];
  titleText: string;
  contentText: string;
}

interface OfficialTab {
  officialTabText: string;
  officialExampleList: OfficialExample[];
}

interface ImageItem {
  imgList: string[];
  title: string;
  content: string;
}

const props = defineProps(['officialTabs']);

// 计算当前tab对应的图片列表
const activeOfficialTab = ref(0);
const currentImgList = computed(() => {
  if (props.officialTabs && props.officialTabs.length > 0) {
    const currentTab = props.officialTabs[activeOfficialTab.value];
    if (currentTab && currentTab.officialExampleList) {
      return currentTab.officialExampleList.map((item: OfficialExample) => ({
        imgList: item.fileUrlList || [],
        title: item.titleText || '',
        content: item.contentText || '',
      } as ImageItem));
    }
  }
  return [] as ImageItem[];
});

// 主tab切换
const switchOfficialTab = (index: number) => {
  activeOfficialTab.value = index;
};
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  .officialTitleBox{
    padding: 0.62rem 0 0.45rem;
    background-color: #fff;
    .officialTitleImg{
      height: 0.33rem;
      margin:0 auto;
    }
  }
  .officialTabArea{
    width: 6.9rem;
    height: 1rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    overflow-x: scroll;
    &::-webkit-scrollbar {
      display: none;
    }

    .officialTabWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;

      .officialTab {
        width: 2rem;
        height: 0.6rem;
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.3rem;
        font-weight: bold;
        cursor: pointer;
      }

      .tabIndicator {
        width: 0.53rem;
        height: .06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
      }
    }
  }
  .imgListBox{
    width: 6.9rem;
    margin: 0 auto;
  }
}
</style>
