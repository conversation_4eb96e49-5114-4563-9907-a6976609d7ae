<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">图片确认</div>
      <div class="rule-box">
        <img class="confirm-img" src="http://vibktprfx-prod-prod-damo-eas-cn-shanghai.oss-cn-shanghai.aliyuncs.com/segment-body/2025-11-06/b29072ff-d38e-4202-8cc9-256b7e959d10/image.png?Expires=1762396131&OSSAccessKeyId=LTAI4FoLmvQ9urWXgSRpDvh1&Signature=HvhiTPpjfNqRZNsnBe%2BQZMC63B0%3D" alt="" srcset="" />
        <div class="btn-group">
          <div @click="handleConfirm"></div>
          <div @click="closeDialog"></div>
        </div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  imgUrl: {
    type: String,
    required: true,
    default: '',
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog', 'userConfirm']);
const closeDialog = () => {
  emits('closeDialog');
};
const handleConfirm = () => {
  emits('userConfirm');
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    margin-top: 0.4rem;
    max-height: 6.2rem;
    .confirm-img {
      width: 4.55rem;
      height: 4rem;
      object-fit: contain;
      display: block;
      margin: 0 auto;
      background-color: #fff;
    }
    .btn-group {
      margin: 0 auto;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/356824/38/4393/22654/690bfd4eFc70a8f74/dcffba456b586160.png) no-repeat;
      background-size: contain;
      width: 4.55rem;
      height: 1.85rem;
      :first-child {
        height: 1.2rem;
      }
      :last-child {
        height: 0.8rem;
      }
    }
  }
}
</style>
