<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">我的留言</div>
      <div class="rule-box">
        <VanField v-model="messageInfo.message" readonly placeholder="说说你的心里话....." rows="5" maxlength="50" autosize type="textarea" />
        <div class="tips">—— From {{ messageInfo.nickName }}</div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';

const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  messageInfo: {
    type: Object,
    default: () => ({}),
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const opendDialog = async () => {};
const message = ref('啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是啊萨达萨达萨达是');
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/239307/12/37381/52947/69085ffeFfe77fc82/740a23cb4063318a.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    margin-top: 0.2rem;
    max-height: 6.2rem;
    overflow-y: auto;
    .van-cell {
      background-color: transparent;
      line-height: 2.43;
      font-size: 0.3rem;
      word-break: break-all;
      --van-field-input-text-color: #7668cb;
    }
    .btn {
      width: 4.24rem;
      margin: 0.5rem auto 0;
    }
    .tips {
      text-align: right;
      position: absolute;
      right: 0.5rem;
      bottom: 1.26rem;
      color: #7668cb;
    }
  }
}
</style>
