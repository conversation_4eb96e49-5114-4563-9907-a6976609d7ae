<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">预约新品</div>
      <div class="rule-box">
        <div class="item-box" v-if="skuList.length">
          <div class="list-item" v-for="item in skuList" :key="item.skuMainPicture">
            <img class="sku-img" :src="item.skuMainPicture" alt="" />
            <div class="van-multi-ellipsis--l2 sku-name">{{ item.skuName }}</div>
            <img @click="gotoSkuPage(item.skuId)" class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/342455/37/18895/6170/69081333F6edfdfd2/a4a02e259e0f387a.png" alt="" />
          </div>
        </div>
        <div class="no-prize" v-else>暂无数据</div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getSkuList } from '../script/ajax';
import { gotoSkuPage } from '@/utils/platforms/jump';

const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const skuList = ref<
  {
    skuMainPicture: string;
    skuName: string;
    skuId: string;
  }[]
>([]);
const opendDialog = async () => {
  skuList.value = await getSkuList(222);
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.88rem;
  height: 6.82rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/348842/12/22841/45672/690acb46Fcce8cf06/267f362647386454.png) no-repeat;
  background-size: 100%;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  text-align: center;

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    font-weight: bold;
    background: radial-gradient(circle, #4a3789 0%, #ebe9ed 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .rule-box {
    .item-box {
      max-height: 8rem;
      overflow-y: auto;
      margin: 0.3rem auto;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.2rem;
      border-radius: 0.3rem;
      font-family: 'OPPOSansM';
      .list-item {
        background: url(../assets/dialog/sku-bg.png) no-repeat;
        background-size: contain;
        width: 2.88rem;
        height: 3.98rem;
        margin: 0 auto;
        padding: 0.3rem 0.5rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        .sku-img {
          width: 100%;
          height: 2.15rem;
          object-fit: contain;
        }
        .sku-name {
          width: 100%;
          font-size: 0.21rem;
        }
        .btn {
          width: 1.85rem;
          margin: 0 auto;
        }
      }
    }
    .AddCar {
      max-height: 5rem;
    }
    .add-btn {
      width: 4.21rem;
      margin: 0.1rem auto;
    }
    .no-prize {
      font-size: 0.4rem;
      text-align: center;
      margin: 1.5rem auto 0;
    }
  }
}
</style>
