<template>
  <div class="page-bg">
    <div class="kv-header">
      <img :src="decoData.kv" alt="" class="kv" />
      <div class="btn-list">
        <img src="./assets/ruleBtn.png" alt="" class="rule-btn" @click="rulePop = true" />
        <img src="./assets/myPrizeBtn.png" alt="" class="my-prize-btn" @click="myPrizePop = true" />
      </div>
    </div>
    <ExchangeTicket />
    <Lottery />
    <Guess />
    <Task />
    <ProductExposure />
    <div class="not-start" v-if="baseInfo.status !== 2" @click="toast"></div>

    <VanPopup teleport="body" v-model:show="rulePop">
      <Rule @close="rulePop = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="myPrizePop">
      <MyPrize v-if="myPrizePop" @close="myPrizePop = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import ExchangeTicket from './component/ExchangeTicket.vue';
import Lottery from './component/Lottery.vue';
import Guess from './component/Guess.vue';
import Task from './component/Task.vue';
import ProductExposure from './component/ProductExposure.vue';
import { inject, onMounted, ref } from 'vue';
import Rule from './Pop/Rule.vue';
import MyPrize from './Pop/MyPrize.vue';
import { BaseInfo } from '@/types/BaseInfo';
import { showToast } from 'vant';
import { userInfo, decoData } from './hooks';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const rulePop = ref(false);
const myPrizePop = ref(false);

const toast = () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始');
  } else if (baseInfo.status === 3) {
    showToast('活动已结束');
  }
};

onMounted(() => {
  if (baseInfo.status === 2) {
    if (userInfo.value.addDrawNum > 0 && userInfo.value.addGuessNum > 0) {
      showToast(`下单成功，获得${userInfo.value.addDrawNum}次抽奖机会+${userInfo.value.addGuessNum}次竞猜机会`);
    }
  }
});
</script>
<style lang="scss">
@font-face {
  font-family: 'FZY4JW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZY4JW/FZY4JW--GB1-0.woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZY4JW/FZY4JW--GB1-0.woff');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'XiaoxinChaoku';
  src: url('https://lzcdn.dianpusoft.cn/fonts/Lenovo-XiaoxinChaoku/Lenovo-XiaoxinChaokuGB.woff2'), url('https://lzcdn.dianpusoft.cn/fonts/Lenovo-XiaoxinChaoku/Lenovo-XiaoxinChaokuGB.woff');
  font-weight: normal;
  font-style: normal;
}
::-webkit-scrollbar {
  display: none;
}
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
<style scoped lang="scss">
.page-bg {
  position: relative;
  background: url('./assets/pageBg.png') repeat;
  background-size: 100%;
  min-height: 100vh;
  padding-bottom: 0.1rem;
}
.kv-header {
  .kv {
    width: 100%;
  }
  .btn-list {
    position: absolute;
    top: 0.83rem;
    right: 0;
    .rule-btn {
      width: 1.43rem;
      margin-bottom: 0.12rem;
    }
    .my-prize-btn {
      width: 1.43rem;
    }
  }
}
.not-start {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}
</style>
