<template>
  <div class="main-view" style="padding-top: unset;position: relative;min-height: 1.334rem;">
    <div v-if="isLoadding">
      <Loading :pageNo="2" />
    </div>
    <div v-else class="result-content-box">
      <img  :src="require('../../asset/logo.png')" class="logo-img" />
      <VanSwipe ref="swiper" :loop="false">
        <van-swipe-item>
          <div class="result-content">
            <img class="bg-img" :src="require('../../asset/aiResBg.png')"/>
            <div class="ai-res-box" style="position: absolute;left: 0.65rem;top: 2.9rem;margin: unset;color: #ec001a;">
              <div class="ai-item-box">
                <div>已分析牙齿总数</div>
                <div class="num-text">{{ aiRes?.teethScanned }}</div>
              </div>
              <div class="ai-item-box">
                <div>有牙结石堆积<br>可见迹象的牙齿数</div>
                <div class="num-text">{{ aiRes?.tartarTeethCount }}</div>
              </div>
              <div class="ai-item-box">
                <div>有牙龈发炎<br>可见迹象的区域数量</div>
                <div class="num-text">{{ aiRes?.gumInflammationCount }}</div>
              </div>
            </div>
          </div>
        </van-swipe-item>
        <van-swipe-item>
          <div class="result-content">
            <img class="bg-img" :src="require(`../../asset/qares-bg.png`)"/>
            <div style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;padding: 2.95rem 0.25rem 0;box-sizing: border-box;">
              <div class="qustion-res-item">
                <div style="display: flex;align-items: flex-start;">
                  <div class="qustion-check-box">
                    <img v-if="qustionRes?.ifHalitosis" style="width: 100%;" :src="require('../../asset/check.png')"/>
                  </div>
                  <div style="line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${qustionRes?.ifHalitosis ? PAGE_CONFIG.mainBgColor : '#000'}`}">口臭</div>
                </div>
                <div class="qustion-desc">口臭是由口腔中的细菌释放出难闻的化合物所导致，这可能是口腔有健康问题的一个迹象。</div>
              </div>
              <div class="qustion-res-item">
                <div style="display: flex;align-items: flex-start;">
                  <div class="qustion-check-box">
                    <img v-if="qustionRes?.ifBleeding" style="width: 100%;" :src="require('../../asset/check.png')"/>
                  </div>
                  <div style="line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${qustionRes?.ifBleeding ? PAGE_CONFIG.mainBgColor : '#000'}`}">出血</div>
                </div>
                <div class="qustion-desc">咀嚼或进食时出血可能提示有牙龈发炎。</div>
              </div>
              <div class="qustion-res-item">
                <div style="display: flex;align-items: flex-start;">
                  <div class="qustion-check-box">
                    <img v-if="qustionRes?.ifDiscomfort" style="width: 100%;" :src="require('../../asset/check.png')"/>
                  </div>
                  <div style="line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${qustionRes?.ifDiscomfort ? PAGE_CONFIG.mainBgColor : '#000'}`}">不适</div>
                </div>
                <div class="qustion-desc">口腔不适可能提示有口腔健康问题。</div>
              </div>
              <img class="qustion-tips-box" v-if="!qustionRes?.ifHalitosis && !qustionRes?.ifBleeding && !qustionRes?.ifDiscomfort" :src="require('../../asset/resdesc1.png')" />
              <img class="qustion-tips-box" v-else :src="require('../../asset/resdesc2.png')" />
              <iframe id="petInfoCard" class="iframeView"></iframe>
              <div class="qustion-bottom">
                <img class="item-btn" :src="require('../../asset/zixun.png')" @click="toEnd">
                <img class="item-btn" :src="require('../../asset/share.png')" @click="share">
              </div>
            </div>
          </div>
        </van-swipe-item>
        <template #indicator="{ active, total }">
          <div class="page-no-box" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">
            <div>结果{{ `${active + 1}/${total}`}}</div>
            <div class="bottom-div">
              <img class="direction-icon" :src="require(`../../asset/direction-left.png`)" @click="swiper.prev()"/>
              <div style="display: flex;flex-direction: row;">
                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 0 ? 1 : 0.2}"></div>
                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 1 ? 1 : 0.2}"></div>
              </div>
              <img class="direction-icon" :src="require(`../../asset/direction-right.png`)" @click="swiper.next()"/>
            </div>
          </div>
        </template>
      </VanSwipe>
    </div>
    <Disclaimers2 :isShow="isShowDisclaimers" @closePopup="isShowDisclaimers = false"/>
    <div id="poster">
      <img :src="require('../../asset/logo.png')" class="post-logo-img"/>
      <div class="post-ai-res-bg" :style="{ backgroundImage: `url(${require('../../asset/posterAiResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}`}">
        <div class="ai-res-time">分析时间：{{ dayjs().format('YYYY-MM-DD') }}</div>
        <div class="ai-res-desc">我们使用人工智能分析了【{{PetInfo?.petNick || qustionRes?.petNick}}】的牙齿照片，结果显示:</div>
        <div class="ai-tooth-img" :style="`background-image: url(${toothImg})`"></div>
        <img class="num-text" style="top: 650px;" :src="require(`../../asset/num/${aiRes?.teethScanned}.png`)">
        <img class="num-text" style="top: 819px;" :src="require(`../../asset/num/${aiRes?.tartarTeethCount}.png`)">
        <img class="num-text" style="top: 988px;" :src="require(`../../asset/num/${aiRes?.gumInflammationCount}.png`)">
      </div>
      <div class="post-question-bg" :style="{ backgroundImage: `url(${require('../../asset/posterQustionResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}` }">
        <img class="res-icon" v-if="qustionRes?.ifHalitosis" style="top: 277px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/274026/20/19905/1637/67ff27d7Fc23b1bd6/95da037f2aa09106.png"/>
        <img class="res-icon" v-else style="top: 277px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/272434/11/21899/910/67ff2c27F80fc8caf/20590e3c2c740675.png"/>
        <img class="res-icon" v-if="qustionRes?.ifBleeding" style="top: 422px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/281715/7/20897/1541/67ff27d7F95674187/2fc1183dc0ebc2bc.png"/>
        <img class="res-icon" v-else style="top: 422px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/277384/33/21871/893/67ff2c27F8bb5f3fc/3aab8ab4f583f5d6.png"/>
        <img class="res-icon" v-if="qustionRes?.ifDiscomfort" style="top: 555px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/283072/13/20581/1912/67ff27d7F78ef96f5/e90f2053c3e817fa.png"/>
        <img class="res-icon" v-else style="top: 555px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/275504/24/21019/1160/67ff2c27F1510aa62/ec543204d7e15c4d.png"/>
        <img class="post-qustion-tips-box" v-if="!qustionRes?.ifHalitosis && !qustionRes?.ifBleeding && !qustionRes?.ifDiscomfort" :src="require('../../asset/resdesc1.png')" />
        <img class="post-qustion-tips-box" v-else :src="require('../../asset/resdesc2.png')" />
      </div>
      <div class="post-bottom-box">
        <div style="margin-top: 50px;">
          识别右侧二维码<br>了解狗狗口腔健康问题潜在迹象
        </div>
        <div class="qrcode-box">
          <img class="code-img" :src="qrCodeActUrl"/>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, computed, ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import Loading from '../../components/Loading.vue';
import Disclaimers2 from '../../components/Disclaimers2.vue';
import { getCheckUpResult, getQaInfo, getRoyalCanin, marsWxUploadToOss } from '../../config/api';

import { useRouter, useRoute } from 'vue-router';
import dayjs from 'dayjs';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import html2canvas from 'html2canvas';
import { httpRequest } from '@/utils/service';
import { lzReportClick } from '@/pages/MARS/lzReport';
import { showFailToast } from 'vant/es';

const router = useRouter();
const route = useRoute();

const store = useStore<RootState>();

const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const pathParams:any = inject('pathParams');
const PetInfo = computed(() => store.state.petInfo);
const ossPicUrl = ref('');
console.log('pathParams', pathParams);
console.log('PetInfo', PetInfo);
const openPopup: any = inject('openPopup');

const { checkId } = route.query;
const isShowDisclaimers = ref(false);

const qustionRes = computed(() => {
  console.log(store.state.stepData);
  if (!store.state.stepData) return {};

  return Object.values(store.state.stepData).reduce((result, data) => ({ ...result, ...data }), {});
});
const qrCodeActUrl = process.env.VUE_APP_WEICHONGYI_QRCODE;
const config = {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
};
const toothImg = ref(store.state.toothImg);
const createpost = ref(false);
const takephotos = async () => {
  const save2 = document.getElementById('poster') as HTMLAnchorElement;
  console.log(save2.offsetWidth, save2.offsetHeight);
  await html2canvas(save2, {
    backgroundColor: null, // 设置图片背景为透明
    scale: 2,
    width: save2.offsetWidth,
    height: save2.offsetWidth / 0.31,
    allowTaint: true,
    useCORS: true,
  }).then((canvas:any) => {
    const context: any = canvas.getContext('2d');
    context.mozImageSmoothingEnabled = false;
    context.webkitImageSmoothingEnabled = false;
    context.msImageSmoothingEnabled = false;
    context.imageSmoothingEnabled = false;
    const src64: any = canvas.toDataURL();
    const newImg: any = document.createElement('img');
    newImg.crossOrigin = 'Anonymous';
    newImg.src = src64;
    canvas.toBlob(async (blob: Blob) => {
      const formData = new FormData();
      formData.append('file', blob, `marscheckurl${new Date().getTime()}.png`);
      const res = await marsWxUploadToOss(formData);
      createpost.value = true;
      ossPicUrl.value = res.data;
    });
  });
};

const aiRes = ref();
const swiper = ref();

const isLoadding = ref(true);
const resNo = ref(0);
const getAiRes = () => {
  getCheckUpResult({ checkId }).then((ressult: any) => {
    if (ressult.status && ressult.res.data.status) {
      isLoadding.value = false;
      store.commit('setAiRes', ressult.res.data);
      aiRes.value = ressult.res.data;
      openPopup();
      setTimeout(() => {
        takephotos();
      }, 3000);
    } else if (resNo.value <= 15) {
      setTimeout(() => {
        getAiRes();
        console.log(resNo.value);
        resNo.value += 1;
      }, 10000);
    } else {
      isLoadding.value = false;
      router.push('/stepError');
    }
  });
};
const init = () => {
  getQaInfo({ checkId }).then((res: any) => {
    store.commit('setQuestionRes', res.data);
  });
  getAiRes();
  lzReportClick('resultPage');
};
const toEnd = () => {
  lzReportClick('veterinarianClick');
  if (!createpost.value) {
    showFailToast('图片正在生成中，预计等待10s后再次点击咨询按钮');
    return;
  }
  const petInfoCard: any = document.querySelector('#petInfoCard');
  getRoyalCanin({ checkId }).then((res: any) => {
    // if (res.data) {
    //   const url1 = `${process.env.VUE_APP_WEICHONGYI_CONSULT_URL}?userKey=${encodeURIComponent(pathParams?.userKey)}&companyID=676344787275836663414260&petid=${res.data}`;
    //   petInfoCard.src = url1 || '';
    //   const url2 = `${process.env.VUE_APP_WEICHONGYI_CONSULT_URL}?userKey=${encodeURIComponent(pathParams?.userKey)}&companyID=676344787275836663414260&petid=${res.data}&picUrl=${encodeURIComponent(ossPicUrl.value)}`;
    //   setTimeout(() => {
    //     window.location.href = url2;
    //   }, 1000);
    // }
    if (res.data) {
      router.push({
        path: '/iframe',
        query: {
          checkId,
          ossPicUrl: ossPicUrl.value,
          petId: res.data,
          userKey: sessionStorage.getItem('WEICHONGYI_USER_KEY'),
        },
      });
    }
  });
};

const share = () => {
  lzReportClick('clickShare');
  router.push({
    path: '/post',
    query: {
      checkId,
    },
  });
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
.iframeView {
  height: 600px;
  position: absolute;
  top: -800px;
}
</style>
