import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/301423/11/18784/601352/6862381eF7c157842/845d970e4c4050d4.png',
  pageBg: '',
  actBgColor: '#cde8df',
  shopNameColor: '#000000',
  ruleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/227786/26/33161/6052/675f9fd3Fd0f13615/7fd60073ce3b9e27.png',
  myPrizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229740/20/35131/2662/675f9fd3F5b13d1de/8cc3c0ec14706d49.png',
  qy1Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/320296/10/12113/43200/68623822Faf5d3658/ba54582c6181f8d7.png',
  qy2Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/310129/18/13328/200882/68623821Fb441245f/140f4a445de2c6c1.png',
  
  qy2BgItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/310318/36/17401/36053/6875ff9fF7a78bd80/52e1449f20c0651d.png',
  qy2BgItemBgArr: [],
  qy3Bg: '',
  hotZoneSetting: {
    bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/309186/16/13151/481499/68623820F1364a472/48b20e95c043e731.png',
    hotZoneList: [],
  },
  showSkuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/313402/18/5441/1466840/68394677F31fdc5ff/b6046fa0ea5daa24.png',

 
  canNotCloseJoinPopup: '1',
  jumpUrl: '',
  isShowJump: true,
  moreActLink: '',

  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/315657/24/17317/198633/6877603eF710c72dd/d05ec1ed580acdc7.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/290797/40/21809/183727/6877603eF42be14ab/6d3e0f8bbecb3f88.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/290797/40/21809/183727/6877603eF42be14ab/6d3e0f8bbecb3f88.png',
  hotZoneList: [],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '复购有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  // app.provide('decoData', decoData);
  app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
