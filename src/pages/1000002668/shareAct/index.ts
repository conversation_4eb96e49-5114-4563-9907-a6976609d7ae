import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
// import { InitRequest } from '@/types/InitRequest';
// import { init, checkStatus } from '@/utils';
// import EventTrackPlugin from '@/plugins/EventTracking';

initRem();

const app = createApp(root);

// 初始化页面
// const config: InitRequest = {
//   disableThresholdPopup: true,
//   backActRefresh: false,
//   disableNotice: true,
// };

// init(config).then(async ({ baseInfo, pathParams }) => {
//   checkStatus(baseInfo, true, true);
//   // 设置页面title
//   document.title = baseInfo.activityName;
//   app.provide('baseInfo', baseInfo);
//   app.provide('pathParams', pathParams);
//   app.use(EventTrackPlugin, {});
// });
document.title = '爱他美 老带新有礼';
setTimeout(() => {
  app.mount('#app');
}, 1000);
