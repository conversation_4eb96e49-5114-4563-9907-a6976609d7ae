// 【#174554】博朗非会老客补发积分
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import App from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';

// 页面自适应
initRem(1080);

const app = createApp(App);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  showUnStartPage: true,
  showFinishedPage: true,
  backActRefresh: false,
  disableNotice: true,
  disableThreshold: true,
  disableShare: true,
};

init(config).then(({ baseInfo, pathParams, userInfo, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '未命名';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('baseUserInfo', userInfo);
  app.provide('decoData', decoData);
  console.log('🚀 ~ init ~ decoData:', decoData);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
